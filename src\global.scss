/**
 * Modern App Theme
 * -----------------------------------------------------
 * IMPORTANT: @use rules must come before any other CSS rules
 */

// Use theme files with modern Sass syntax
@use './theme/index.scss' as *;

/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import '@ionic/angular/css/palettes/dark.system.css';

/**
 * Custom App Styles
 * -----------------------------------------------------
 */

/**
 * Global App Styles
 * -----------------------------------------------------
 */

// Set global font family
* {
  font-family: var(--app-font-family);
}

// Ensure our theme styles have higher specificity
ion-content.app-page {
  --background: transparent !important;
}



// Override Ionic CSS variables with our theme
:root {
  --ion-font-family: var(--app-font-family);
  --ion-color-primary: var(--app-primary);
  --ion-color-primary-rgb: var(--app-primary-rgb);
  --ion-color-primary-contrast: var(--app-primary-contrast);
  --ion-color-primary-shade: var(--app-primary-shade);
  --ion-color-primary-tint: var(--app-primary-tint);
}

// Global body styles
body {
  font-family: var(--app-font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Remove default Ionic styling that conflicts with our theme
ion-content {
  --background: transparent !important;
}

// Ensure our theme styles override Ionic defaults
.app-page.has-gradient-bg .app-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.9;
  }
}

.app-page.has-gradient-bg .app-floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;

  .shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    animation: float 6s ease-in-out infinite;

    &.shape-1 {
      width: 120px;
      height: 120px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.shape-2 {
      width: 80px;
      height: 80px;
      top: 70%;
      right: 15%;
      animation-delay: 2s;
    }

    &.shape-3 {
      width: 60px;
      height: 60px;
      top: 30%;
      right: 25%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.app-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.8s ease-out;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.3);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Enhanced Dashboard Responsive Grid System
// -----------------------------------------------------------------------------
.dashboard-stats {
  .stats-grid {
    display: grid;
    gap: var(--app-spacing-md);
    grid-template-columns: 1fr;

    // Tablet: 2 columns
    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: var(--app-spacing-lg);
    }

    // Desktop: 2 columns with better spacing
    @media (min-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
      gap: var(--app-spacing-xl);
    }

    // Large Desktop: 4 columns
    @media (min-width: 1400px) {
      grid-template-columns: repeat(4, 1fr);
      gap: var(--app-spacing-lg);
    }

    // Extra Large: 4 columns with more spacing
    @media (min-width: 1600px) {
      gap: var(--app-spacing-xl);
    }
  }
}

.stat-card {
  position: relative;
  padding: var(--app-spacing-lg);

  .stat-card-header {
    display: flex;
    align-items: flex-start;
    gap: var(--app-spacing-md);
    margin-bottom: var(--app-spacing-md);
  }

  .stat-card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--app-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    ion-icon {
      font-size: 1.5rem;
      color: var(--app-white);
    }
  }

  .stat-card-info {
    flex: 1;
    min-width: 0;
  }

  .stat-card-title {
    font-size: var(--app-font-size-sm);
    font-weight: var(--app-font-weight-semibold);
    color: var(--app-gray-600);
    margin: 0 0 var(--app-spacing-xs) 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .stat-card-value {
    font-size: var(--app-font-size-3xl);
    font-weight: var(--app-font-weight-bold);
    color: var(--app-gray-800);
    margin: 0;
    line-height: 1;
  }

  .stat-card-action {
    --color: var(--app-gray-600);
    --color-hover: var(--app-primary);
    font-size: var(--app-font-size-sm);
    font-weight: var(--app-font-weight-medium);
    margin: 0;
    padding: var(--app-spacing-sm) 0;

    &:hover {
      --color: var(--app-primary);
    }
  }

  // Status-specific styling
  &.stat-card-warning .stat-card-icon {
    background: var(--app-warning);
  }

  &.stat-card-success .stat-card-icon {
    background: var(--app-success);
  }

  &.stat-card-danger .stat-card-icon {
    background: var(--app-danger);
  }

  &.stat-card-info .stat-card-icon {
    background: var(--app-info);
  }
}

// Recent requests styling
.recent-requests-list {
  .app-list-item {
    padding: var(--app-spacing-md) 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }
  }
}

// Enhanced Requests Page Responsive Styles
// -----------------------------------------------------------------------------
.search-filter-card {
  .search-filter-content {
    display: grid;
    gap: var(--app-spacing-md);

    // Tablet and up: side-by-side layout
    @media (min-width: 768px) {
      grid-template-columns: 2fr 1fr;
      gap: var(--app-spacing-lg);
    }

    // Large screens: more spacing
    @media (min-width: 1200px) {
      gap: var(--app-spacing-xl);
    }
  }
}

.requests-grid {
  display: grid;
  gap: var(--app-spacing-md);
  grid-template-columns: 1fr;

  // Tablet: 1 column with better spacing
  @media (min-width: 768px) {
    gap: var(--app-spacing-lg);
  }

  // Desktop: 2 columns
  @media (min-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--app-spacing-lg);
  }

  // Large Desktop: 3 columns
  @media (min-width: 1400px) {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--app-spacing-lg);
  }

  // Extra Large: 3 columns with more spacing
  @media (min-width: 1600px) {
    gap: var(--app-spacing-xl);
  }
}

.request-card {
  .request-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .request-id-section {
      flex: 1;
      min-width: 0;

      .request-id {
        font-size: var(--app-font-size-lg);
        font-weight: var(--app-font-weight-bold);
        color: var(--app-gray-800);
        margin: 0 0 var(--app-spacing-xs) 0;
      }
    }
  }

  .request-details {
    .app-list-item.compact {
      padding: var(--app-spacing-sm) 0;

      &:last-child {
        border-bottom: none;
      }

      .app-list-icon.small {
        width: 32px;
        height: 32px;

        ion-icon {
          font-size: 1rem;
        }
      }
    }
  }

  .request-actions {
    display: flex;
    flex-direction: column;
    gap: var(--app-spacing-md);
    padding-top: var(--app-spacing-md);
    border-top: 1px solid rgba(0, 0, 0, 0.05);

    .action-buttons {
      display: flex;
      gap: var(--app-spacing-sm);
      flex-wrap: wrap;
    }

    .details-button {
      --color: var(--app-gray-600);
      --color-hover: var(--app-primary);
      font-size: var(--app-font-size-sm);
      font-weight: var(--app-font-weight-medium);
      margin: 0;
      padding: var(--app-spacing-sm) 0;

      &:hover {
        --color: var(--app-primary);
      }
    }
  }
}

// Loading and error states
.loading-content, .error-content, .no-results-content {
  padding: var(--app-spacing-xl);

  .loading-spinner {
    --color: var(--app-primary);
    width: 48px;
    height: 48px;
  }

  .error-icon, .no-results-icon {
    ion-icon {
      font-size: 4rem;
    }
  }
}

// Enhanced Responsive Layout System
// -----------------------------------------------------------------------------
.app-container {
  position: relative;
  z-index: var(--app-z-content);
  min-height: 100vh;
  padding: var(--app-spacing-md);

  @media (min-width: 768px) {
    padding: var(--app-spacing-lg);
  }

  @media (min-width: 1200px) {
    padding: var(--app-spacing-xl) var(--app-spacing-lg);
  }

  @media (min-width: 1600px) {
    padding: var(--app-spacing-xl);
  }
}

.app-content {
  margin: 0 auto;
  width: 100%;
  max-width: 1200px; // Default max-width

  // Content width variants for different page types
  &.app-content-narrow {
    max-width: 600px; // For login, signup forms
  }

  &.app-content-medium {
    max-width: 900px; // For profile, single-column layouts
  }

  &.app-content-wide {
    max-width: 1400px; // For dashboard, requests grid

    @media (min-width: 1600px) {
      max-width: 1600px;
    }
  }

  &.app-content-full {
    max-width: none; // For full-width layouts
    padding: 0 var(--app-spacing-md);

    @media (min-width: 768px) {
      padding: 0 var(--app-spacing-lg);
    }
  }
}

// Enhanced Header Spacing
// -----------------------------------------------------------------------------
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--app-spacing-lg);
  padding: 0 var(--app-spacing-xs);

  @media (min-width: 768px) {
    margin-bottom: var(--app-spacing-xl);
    padding: 0 var(--app-spacing-sm);
  }

  @media (min-width: 1200px) {
    padding: 0 var(--app-spacing-md);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: var(--app-spacing-md);

    @media (min-width: 768px) {
      gap: var(--app-spacing-lg);
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: var(--app-spacing-sm);

    @media (min-width: 768px) {
      gap: var(--app-spacing-md);
    }
  }

  .header-title {
    font-size: var(--app-font-size-xl);
    font-weight: var(--app-font-weight-bold);
    color: var(--app-white);
    margin: 0;

    @media (min-width: 768px) {
      font-size: var(--app-font-size-2xl);
    }
  }
}

// Ensure proper stacking context
.app-background {
  z-index: var(--app-z-background);
}

/**
 * Global Ion-Modal Glassmorphic Styling
 * -----------------------------------------------------
 * Consistent modal styling across the entire application
 * using CSS shadow parts and custom properties
 */

// Modal backdrop styling
ion-modal {
  // Use CSS shadow parts to style the backdrop
  &::part(backdrop) {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8px);
  }

  // Use CSS shadow parts to style the content
  &::part(content) {
    // Glassmorphic background
    --background: var(--app-glass-bg);
    --border-radius: var(--app-radius-2xl);
    --border-width: 1px;
    --border-style: solid;
    --border-color: var(--app-glass-border);

    // Enhanced shadow for depth
    box-shadow: var(--app-shadow-2xl);
    backdrop-filter: var(--app-glass-backdrop);

    // Responsive sizing - much wider on larger screens
    --width: 95%;
    --max-width: 500px;
    --max-height: 90vh;

    // Tablet: wider modal
    @media (min-width: 768px) {
      --width: 85%;
      --max-width: 700px;
      --max-height: 85vh;
    }

    // Desktop: significantly wider
    @media (min-width: 1024px) {
      --width: 80%;
      --max-width: 900px;
      --max-height: 80vh;
    }

    // Large desktop: even wider for better use of space
    @media (min-width: 1400px) {
      --width: 70%;
      --max-width: 1100px;
      --max-height: 75vh;
    }

    // Extra large screens: maximum width
    @media (min-width: 1600px) {
      --width: 60%;
      --max-width: 1200px;
      --max-height: 70vh;
    }
  }

  // Enhanced modal animation
  &.modal-entering::part(content) {
    animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  &.modal-leaving::part(content) {
    animation: modalSlideOut 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
  }
}

// Modal header styling (targets ion-header inside modals)
ion-modal ion-header {
  ion-toolbar {
    // Glassmorphic header
    --background: rgba(255, 255, 255, 0.8);
    --border-color: var(--app-glass-border);
    backdrop-filter: var(--app-glass-backdrop);

    // Enhanced title styling
    ion-title {
      font-size: var(--app-font-size-lg);
      font-weight: var(--app-font-weight-semibold);
      color: var(--app-gray-800);
    }

    // Button styling in header
    ion-button {
      --color: var(--app-gray-600);
      --color-hover: var(--app-primary);
      font-weight: var(--app-font-weight-medium);
      transition: all var(--app-transition-normal);

      &[strong="true"] {
        --color: var(--app-primary);
        --color-hover: var(--app-primary-shade);
        font-weight: var(--app-font-weight-semibold);
      }

      &:hover {
        transform: translateY(-1px);
      }
    }
  }
}

// Modal content styling
ion-modal ion-content {
  // Enhanced content background
  --background: transparent;

  // Form styling inside modals
  form {
    display: flex;
    flex-direction: column;
    gap: var(--app-spacing-lg);

    // Better form layout on larger screens
    @media (min-width: 768px) {
      gap: var(--app-spacing-xl);
      padding: var(--app-spacing-md);
    }

    // Grid layout for wider forms on desktop
    @media (min-width: 1024px) {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--app-spacing-lg);
      align-items: start;

      // Full width items (like roles select, password, status)
      ion-item:has(ion-select[multiple]),
      ion-item:has(ion-input[type="password"]),
      ion-item:last-child {
        grid-column: 1 / -1;
      }
    }

    // Even better spacing on large screens
    @media (min-width: 1400px) {
      gap: var(--app-spacing-xl);
      padding: var(--app-spacing-lg);
    }
  }

  // Enhanced ion-item styling in modals
  ion-item {
    --background: rgba(255, 255, 255, 0.7);
    --border-radius: var(--app-radius-lg);
    --border-width: 1px;
    --border-style: solid;
    --border-color: var(--app-gray-200);
    --padding-start: var(--app-spacing-md);
    --padding-end: var(--app-spacing-md);
    --min-height: 3.5rem;

    margin-bottom: var(--app-spacing-md);
    backdrop-filter: blur(10px);
    transition: all var(--app-transition-normal);

    &:hover {
      --background: rgba(255, 255, 255, 0.9);
      --border-color: var(--app-gray-300);
      transform: translateY(-1px);
    }

    &:focus-within {
      --border-color: var(--app-primary);
      --background: var(--app-white);
      box-shadow: 0 0 0 3px rgba(var(--app-primary-rgb), 0.1);
    }

    // Enhanced input styling
    ion-input {
      --color: var(--app-gray-800);
      --placeholder-color: var(--app-gray-400);
      font-size: var(--app-font-size-base);

      &.ion-invalid {
        --color: var(--app-danger);
      }
    }

    // Enhanced select styling
    ion-select {
      --color: var(--app-gray-800);
      --placeholder-color: var(--app-gray-400);
      font-size: var(--app-font-size-base);
    }
  }
}

// Modal animations
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
  }
}

// Enhanced mobile modal styling
@media (max-width: 767px) {
  ion-modal::part(content) {
    --width: 100%;
    --max-width: 100%;
    --border-radius: var(--app-radius-2xl) var(--app-radius-2xl) 0 0;
    --max-height: 95vh;

    // Position at bottom on mobile for better UX
    margin-top: auto;
  }

  // Adjust header padding on mobile
  ion-modal ion-header ion-toolbar {
    --padding-start: var(--app-spacing-md);
    --padding-end: var(--app-spacing-md);
  }

  // Adjust content padding on mobile
  ion-modal ion-content {
    --padding-start: var(--app-spacing-md);
    --padding-end: var(--app-spacing-md);
    --padding-top: var(--app-spacing-md);
    --padding-bottom: var(--app-spacing-md);
  }
}

// Dark mode support for modals
@media (prefers-color-scheme: dark) {
  ion-modal {
    &::part(backdrop) {
      background: rgba(0, 0, 0, 0.6);
    }

    &::part(content) {
      --background: rgba(30, 30, 30, 0.95);
      --border-color: rgba(255, 255, 255, 0.1);
    }
  }

  ion-modal ion-header ion-toolbar {
    --background: rgba(40, 40, 40, 0.9);
    --border-color: rgba(255, 255, 255, 0.1);

    ion-title {
      color: var(--app-white);
    }

    ion-button {
      --color: rgba(255, 255, 255, 0.8);
      --color-hover: var(--app-primary-tint);

      &[strong="true"] {
        --color: var(--app-primary-tint);
      }
    }
  }

  ion-modal ion-content ion-item {
    --background: rgba(50, 50, 50, 0.8);
    --border-color: rgba(255, 255, 255, 0.1);

    &:hover {
      --background: rgba(60, 60, 60, 0.9);
      --border-color: rgba(255, 255, 255, 0.2);
    }

    &:focus-within {
      --border-color: var(--app-primary-tint);
      --background: rgba(40, 40, 40, 0.95);
    }

    ion-input, ion-select {
      --color: var(--app-white);
      --placeholder-color: rgba(255, 255, 255, 0.5);
    }
  }
}

// Accessibility enhancements
ion-modal {
  // Focus management
  &[aria-hidden="false"] {
    ion-content {
      // Ensure first focusable element gets focus
      ion-item:first-of-type ion-input,
      ion-item:first-of-type ion-select {
        --color: var(--app-gray-800);
      }
    }
  }

  // High contrast mode support
  @media (prefers-contrast: high) {
    &::part(content) {
      --border-width: 2px;
      --border-color: var(--app-gray-800);
    }

    ion-header ion-toolbar {
      --border-color: var(--app-gray-800);
    }

    ion-content ion-item {
      --border-width: 2px;
      --border-color: var(--app-gray-600);
    }
  }

  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    &.modal-entering::part(content),
    &.modal-leaving::part(content) {
      animation: none;
    }

    ion-header ion-toolbar ion-button,
    ion-content ion-item {
      transition: none;
    }
  }
}
