import {Component, OnInit} from '@angular/core';
import {IonApp, IonRouterOutlet} from '@ionic/angular/standalone';
import {PermissionInitService} from './services/permission-init.service';
import {PermissionDebugService} from './services/permission-debug.service';
import {environment} from '../environments/environment';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  imports: [IonApp, IonRouterOutlet],
})
export class AppComponent implements OnInit {

  constructor(
    private permissionInitService: PermissionInitService,
    private permissionDebugService: PermissionDebugService
  ) {
    // Expose debug service to global scope for testing
    (window as any).permissionDebug = this.permissionDebugService;
  }

  async ngOnInit() {
    // Initialize permissions before any routing occurs
    try {
      await this.permissionInitService.initializePermissions();
      console.log('✅ App permissions initialized successfully');

      // Debug current state in development
      if (!environment.production) {
        this.permissionDebugService.debugPermissionState();
      }
    } catch (error) {
      console.error('❌ Failed to initialize app permissions:', error);
    }
  }
}
