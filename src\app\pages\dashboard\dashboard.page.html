<ion-content [fullscreen]="true" class="app-page has-gradient-bg">
  <!-- Background Elements -->
  <div class="app-background">
    <div class="app-floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>

  <!-- Main Dashboard Container -->
  <div class="app-container">
    <div class="app-content app-content-wide">
      <!-- Header Section -->
      <div class="app-header">
        <div class="header-left">
          <ion-button fill="clear" class="app-header-button">
            <ion-menu-button slot="icon-only"></ion-menu-button>
          </ion-button>
          <h1 class="header-title">Dashboard</h1>
        </div>
        <div class="header-right">
          <ion-button fill="clear" class="app-header-button">
            <ion-icon name="notifications-outline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button fill="clear" class="app-header-button">
            <ion-icon name="settings-outline" slot="icon-only"></ion-icon>
          </ion-button>
        </div>
      </div>
      <!-- Stats Cards -->
      <div class="dashboard-stats mb-xl">
        <div class="stats-grid">
          <!-- Pending Requests Card -->
          <div class="app-card stat-card stat-card-warning">
            <div class="stat-card-header">
              <div class="stat-card-icon">
                <ion-icon name="time-outline"></ion-icon>
              </div>
              <div class="stat-card-info">
                <h3 class="stat-card-title">Pending Requests</h3>
                <div class="stat-card-value">{{ stats.pendingRequests }}</div>
              </div>
            </div>
            <ion-button fill="clear" class="stat-card-action" [routerLink]="['/menu/requests']">
              <span>View All</span>
              <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
            </ion-button>
          </div>

          <!-- Approved Requests Card -->
          <div class="app-card stat-card stat-card-success">
            <div class="stat-card-header">
              <div class="stat-card-icon">
                <ion-icon name="checkmark-circle-outline"></ion-icon>
              </div>
              <div class="stat-card-info">
                <h3 class="stat-card-title">Approved Requests</h3>
                <div class="stat-card-value">{{ stats.approvedRequests }}</div>
              </div>
            </div>
            <ion-button fill="clear" class="stat-card-action" [routerLink]="['/menu/approvals']">
              <span>View All</span>
              <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
            </ion-button>
          </div>

          <!-- Rejected Requests Card -->
          <div class="app-card stat-card stat-card-danger">
            <div class="stat-card-header">
              <div class="stat-card-icon">
                <ion-icon name="close-circle-outline"></ion-icon>
              </div>
              <div class="stat-card-info">
                <h3 class="stat-card-title">Rejected Requests</h3>
                <div class="stat-card-value">{{ stats.rejectedRequests }}</div>
              </div>
            </div>
            <ion-button fill="clear" class="stat-card-action" [routerLink]="['/menu/approvals']">
              <span>View All</span>
              <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
            </ion-button>
          </div>

          <!-- Total Users Card -->
          <div class="app-card stat-card stat-card-info">
            <div class="stat-card-header">
              <div class="stat-card-icon">
                <ion-icon name="people-outline"></ion-icon>
              </div>
              <div class="stat-card-info">
                <h3 class="stat-card-title">Total Users</h3>
                <div class="stat-card-value">{{ stats.totalUsers }}</div>
              </div>
            </div>
            <ion-button fill="clear" class="stat-card-action" [routerLink]="['/menu/users']">
              <span>View All</span>
              <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>

      <!-- Recent Requests -->
      <div class="app-card">
        <div class="card-header mb-lg">
          <h2 class="text-2xl font-bold text-gray-800 m-0">Recent Requests</h2>
          <p class="text-gray-600 text-sm m-0 mt-xs">Latest approval requests from your team</p>
        </div>

        <div class="recent-requests-list">
          <div *ngFor="let request of recentRequests" class="app-list-item">
            <div class="app-list-icon">
              <ion-icon
                [name]="getRequestIcon(request.status)"
                [class]="getRequestIconClass(request.status)">
              </ion-icon>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">{{ request.asset }}</div>
              <div class="app-list-subtitle">{{ request.technician }} • {{ request.date }}</div>
            </div>
            <div class="flex items-center gap-sm">
              <div class="app-badge" [ngClass]="getStatusBadgeClass(request.status)">
                {{ request.status }}
              </div>
              <ion-icon name="chevron-forward-outline" class="app-list-action"></ion-icon>
            </div>
          </div>
        </div>

        <div class="mt-lg">
          <ion-button expand="block" class="app-button app-button-outline" [routerLink]="['/menu/requests']">
            <div class="app-button-content">
              <ion-icon name="list-outline" class="app-button-icon"></ion-icon>
              <span>View All Requests</span>
            </div>
          </ion-button>
        </div>
      </div>
    </div>
  </div>
</ion-content>
