{"name": "approval-flow", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ionic serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@apollo/client": "^3.13.8", "@auth0/angular-jwt": "^5.2.0", "@capacitor/app": "7.0.1", "@capacitor/core": "7.2.0", "@capacitor/haptics": "7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/angular": "^8.0.0", "@ionic/storage-angular": "^4.0.0", "@ngrx/effects": "^19.1.0", "@ngrx/entity": "^19.1.0", "@ngrx/store": "^19.1.0", "@ngrx/store-devtools": "^19.1.0", "apollo-angular": "^10.0.3", "graphql": "^16.11.0", "ionicons": "^7.0.0", "ngx-permissions": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.0", "@angular-eslint/builder": "^19.0.0", "@angular-eslint/eslint-plugin": "^19.0.0", "@angular-eslint/eslint-plugin-template": "^19.0.0", "@angular-eslint/schematics": "^19.0.0", "@angular-eslint/template-parser": "^19.0.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@angular/language-service": "^19.0.0", "@capacitor/cli": "7.2.0", "@ionic/angular-toolkit": "^12.0.0", "@tailwindcss/aspect-ratio": "latest", "@tailwindcss/forms": "latest", "@tailwindcss/line-clamp": "latest", "@tailwindcss/typography": "latest", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "autoprefixer": "^10.4.7", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ngx-tailwind": "^4.0.0", "postcss": "^8.4.14", "tailwindcss": "^3.1.6", "typescript": "~5.6.3"}, "description": "An Ionic project"}