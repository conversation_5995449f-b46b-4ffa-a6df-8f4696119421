import {Component, OnInit, ViewEncapsulation} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {Router, RouterLink} from '@angular/router';
import {
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonText,
  IonTitle,
  IonToolbar
} from '@ionic/angular/standalone';
import {addIcons} from 'ionicons';
import {
  lockClosedOutline,
  logInOutline,
  mailOutline,
  personAddOutline,
  personOutline
} from 'ionicons/icons';

@Component({
  selector: 'app-signup',
  templateUrl: './signup.page.html',
  styleUrls: ['./signup.page.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonButton,
    IonIcon,
    IonItem,
    IonLabel,
    IonInput,
    IonText,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterLink
  ]
})
export class SignupPage implements OnInit {
  signupForm: FormGroup;
  isSubmitted = false;
  passwordMatching = true;

  constructor(private formBuilder: FormBuilder, private router: Router) {
    addIcons({
      'mail': mailOutline,
      'lock-closed': lockClosedOutline,
      'log-in': logInOutline,
      'person-add': personAddOutline,
      'person': personOutline
    });
  }

  ngOnInit() {
    this.signupForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    });
  }

  get errorControl() {
    return this.signupForm.controls;
  }

  onSubmit() {
    this.isSubmitted = true;

    // Check if passwords match
    const password = this.signupForm.get('password')?.value;
    const confirmPassword = this.signupForm.get('confirmPassword')?.value;
    this.passwordMatching = password === confirmPassword;

    if (this.signupForm.valid && this.passwordMatching) {
      console.log('Signup form is valid');
      // In a real app, you would call an auth service here
      // For now, just navigate to the login page
      this.router.navigate(['/login']);
    } else {
      console.log('Please provide all the required values!');
    }
  }
}
