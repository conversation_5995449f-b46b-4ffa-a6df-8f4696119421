import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonMenuButton,
  IonModal,
  IonRefresher,
  IonRefresherContent,
  IonSelect,
  IonSelectOption,
  IonSpinner,
  IonTextarea,
  IonTitle,
  IonToolbar,
} from '@ionic/angular/standalone';
import { NgxPermissionsModule } from 'ngx-permissions';
import {addIcons} from 'ionicons';
import {
  addOutline,
  alertCircleOutline,
  businessOutline,
  calendarOutline,
  checkmarkCircleOutline,
  chevronForwardOutline,
  closeCircleOutline,
  closeOutline,
  documentTextOutline,
  ellipsisHorizontalOutline,
  filterOutline,
  hourglassOutline,
  locationOutline,
  personOutline,
  refreshOutline,
  saveOutline,
  searchOutline,
  timeOutline
} from 'ionicons/icons';
import {Store} from '@ngrx/store';
import {Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';

import {Request, RequestCreateDto} from '../../models/request.model';
import {AppState} from '../../store';
import * as RequestActions from '../../store/requests/request.actions';
import * as RequestSelectors from '../../store/requests/request.selectors';
import {ToastService} from '../../services/toast.service';

@Component({
  selector: 'app-requests',
  templateUrl: './requests.page.html',
  styleUrls: ['./requests.page.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPermissionsModule,
    IonContent,
    IonButton,
    IonButtons,
    IonHeader,
    IonIcon,
    IonInput,
    IonItem,
    IonLabel,
    IonMenuButton,
    IonModal,
    IonRefresher,
    IonRefresherContent,
    IonSelect,
    IonSelectOption,
    IonSpinner,
    IonTextarea,
    IonTitle,
    IonToolbar
  ]
})
export class RequestsPage implements OnInit, OnDestroy {
  // Search and filter properties
  searchTerm: string = '';
  statusFilter: string = 'all';

  // Store selectors
  requests$!: Observable<Request[]>;
  loading$!: Observable<boolean>;
  error$!: Observable<any>;

  // Local state
  requests: Request[] = [];
  filteredRequests: Request[] = [];

  // Modal state
  isModalOpen = false;
  requestForm!: FormGroup;

  // For cleanup
  private destroy$ = new Subject<void>();

  constructor(
    private store: Store<AppState>,
    private router: Router,
    private formBuilder: FormBuilder,
    private toastService: ToastService
  ) {
    // Add icons
    addIcons({
      'filter': filterOutline,
      'search': searchOutline,
      'search-outline': searchOutline,
      'add': addOutline,
      'add-outline': addOutline,
      'time': timeOutline,
      'time-outline': timeOutline,
      'checkmark-circle': checkmarkCircleOutline,
      'checkmark-circle-outline': checkmarkCircleOutline,
      'checkmark-outline': checkmarkCircleOutline,
      'close-circle': closeCircleOutline,
      'close-circle-outline': closeCircleOutline,
      'ellipsis-horizontal': ellipsisHorizontalOutline,
      'calendar': calendarOutline,
      'location': locationOutline,
      'location-outline': locationOutline,
      'person': personOutline,
      'person-outline': personOutline,
      'business': businessOutline,
      'document-text': documentTextOutline,
      'document-text-outline': documentTextOutline,
      'hourglass': hourglassOutline,
      'alert-circle': alertCircleOutline,
      'alert-circle-outline': alertCircleOutline,
      'funnel-outline': filterOutline,
      'refresh-outline': refreshOutline,
      'chevron-forward-outline': chevronForwardOutline,
      'close-outline': closeOutline,
      'save-outline': saveOutline
    });
  }

  ngOnInit() {
    // Initialize store selectors
    this.requests$ = this.store.select(RequestSelectors.selectAllRequests);
    this.loading$ = this.store.select(RequestSelectors.selectRequestLoading);
    this.error$ = this.store.select(RequestSelectors.selectRequestError);

    // Initialize form
    this.requestForm = this.createRequestForm();

    // Load requests from the store
    this.store.dispatch(RequestActions.loadRequests());

    // Subscribe to requests and update filtered requests
    this.requests$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(requests => {
      this.requests = requests;
      this.applyFilters();
    });

    // Subscribe to error state for notifications
    this.error$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(error => {
      if (error) {
        this.toastService.showError(error.message || 'An error occurred');
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Method to handle refresh
  handleRefresh(event: any) {
    // Dispatch action to reload requests
    this.store.dispatch(RequestActions.loadRequests());

    // Complete the refresh after a short delay
    setTimeout(() => {
      event.target.complete();
    }, 1000);
  }

  // Method to apply filters and search
  applyFilters() {
    this.filteredRequests = this.requests.filter(request => {
      // Apply status filter
      if (this.statusFilter !== 'all' && request.status.toLowerCase() !== this.statusFilter.toLowerCase()) {
        return false;
      }

      // Apply search term
      if (this.searchTerm && this.searchTerm.trim() !== '') {
        const term = this.searchTerm.toLowerCase();
        return (
          request.uuid.toLowerCase().includes(term) ||
          request.title.toLowerCase().includes(term) ||
          request.description.toLowerCase().includes(term) ||
          (request.requestedBy?.fullName || '').toLowerCase().includes(term)
        );
      }

      return true;
    });
  }

  // Method to handle search input
  onSearchChange(event: any) {
    this.searchTerm = event.detail.value;
    this.applyFilters();
  }

  // Method to handle status filter change
  onStatusFilterChange(event: any) {
    this.statusFilter = event.detail.value;
    this.applyFilters();
  }

  // Method to get status badge class
  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'app-badge-warning';
      case 'approved':
        return 'app-badge-success';
      case 'rejected':
        return 'app-badge-danger';
      case 'completed':
        return 'app-badge-info';
      default:
        return 'app-badge-gray';
    }
  }

  // Method to get status color (legacy - keeping for compatibility)
  getStatusColor(status: string): string {
    switch (status) {
      case 'Pending':
        return 'pending';
      case 'Approved':
        return 'approved';
      case 'Rejected':
        return 'rejected';
      case 'Completed':
        return 'completed';
      default:
        return '';
    }
  }

  // Method to get status icon (legacy - keeping for compatibility)
  getStatusIcon(status: string): string {
    switch (status) {
      case 'Pending':
        return 'hourglass';
      case 'Approved':
        return 'checkmark-circle';
      case 'Rejected':
        return 'close-circle';
      case 'Completed':
        return 'checkmark-circle';
      default:
        return '';
    }
  }

  // New methods for the updated UI
  clearSearch() {
    this.searchTerm = '';
    this.applyFilters();
  }

  clearFilters() {
    this.searchTerm = '';
    this.statusFilter = 'all';
    this.applyFilters();
  }

  // Create request form
  private createRequestForm(): FormGroup {
    return this.formBuilder.group({
      title: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', [Validators.required, Validators.maxLength(500)]]
    });
  }

  // Open create request modal
  addNewRequest() {
    this.requestForm.reset();
    this.isModalOpen = true;
  }

  // Close modal
  closeModal() {
    this.isModalOpen = false;
    this.requestForm.reset();
  }

  // Save request
  saveRequest() {
    if (this.requestForm.valid) {
      const formValue = this.requestForm.value;
      const requestDto: RequestCreateDto = {
        title: formValue.title.trim(),
        description: formValue.description.trim()
      };

      this.store.dispatch(RequestActions.saveRequest({ requestDto }));
      this.toastService.showSuccess('Request created successfully');
      this.closeModal();
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.requestForm.controls).forEach(key => {
        this.requestForm.get(key)?.markAsTouched();
      });
    }
  }

  approveRequest(request: any) {
    // TODO: Implement approve request logic
    console.log('Approve request:', request.requestId);
  }

  rejectRequest(request: any) {
    // TODO: Implement reject request logic
    console.log('Reject request:', request.requestId);
  }

  completeRequest(request: any) {
    // TODO: Implement complete request logic
    console.log('Complete request:', request.requestId);
  }

  viewRequestDetails(request: Request) {
    this.router.navigate(['/menu/request-details', request.uuid]);
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
