<ion-content [fullscreen]="true" class="app-page has-gradient-bg">
  <!-- Background Elements -->
  <div class="app-background">
    <div class="app-floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>

  <!-- Main Profile Container -->
  <div class="app-container">
    <div class="app-content app-content-medium">
      <!-- Header Section -->
      <div class="app-header">
        <div class="header-left">
          <ion-button fill="clear" class="app-header-button" (click)="goBack()">
            <ion-icon name="arrow-back-outline" slot="icon-only"></ion-icon>
          </ion-button>
          <h1 class="header-title">Profile</h1>
        </div>
        <div class="header-right">
          <ion-button fill="clear" class="app-header-button" (click)="openSettings()">
            <ion-icon name="settings-outline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button fill="clear" class="app-header-button">
            <ion-icon name="share-outline" slot="icon-only"></ion-icon>
          </ion-button>
        </div>
      </div>

      <!-- Profile Card -->
      <div class="app-card">
        <!-- Avatar Section -->
        <div class="flex flex-col items-center mb-xl">
          <div class="app-avatar mb-lg">
            <div class="app-avatar-image">
              <ion-icon name="person" class="app-avatar-icon"></ion-icon>
            </div>
            <ion-button fill="clear" class="app-avatar-edit" (click)="editAvatar()">
              <ion-icon name="camera-outline" slot="icon-only"></ion-icon>
            </ion-button>
          </div>
          <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-800 m-0 mb-xs">
              {{ isLoadingProfile ? 'Loading...' : (currentUser?.fullName || user.name) }}
            </h2>
            <p class="text-lg text-gray-600 m-0 mb-sm font-medium">
              {{ isLoadingProfile ? 'Loading...' : (getRolesDisplayString() || user.role) }}
            </p>
            <div class="app-status-indicator" *ngIf="!isLoadingProfile">
              <div class="app-status-dot" [class.active]="currentUser?.status === 'ACTIVE'"></div>
              <span class="app-status-text" [class.active]="currentUser?.status === 'ACTIVE'">
                {{ currentUser?.status || 'Active' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Profile Details -->
        <div class="mb-xl">
          <!-- Email -->
          <div class="app-list-item">
            <div class="app-list-icon">
              <ion-icon name="mail-outline"></ion-icon>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">Email</div>
              <div class="app-list-subtitle">{{ currentUser?.email || user.email }}</div>
            </div>
            <ion-button fill="clear" size="small" (click)="editField('email')">
              <ion-icon name="create-outline" slot="icon-only" class="text-gray-400"></ion-icon>
            </ion-button>
          </div>

          <!-- Role -->
          <div class="app-list-item">
            <div class="app-list-icon">
              <ion-icon name="shield-outline"></ion-icon>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">Role</div>
              <div class="app-badge" [ngClass]="getRoleBadgeClass(user.role)">
                {{ user.role }}
              </div>
            </div>
          </div>

          <!-- Member Since -->
          <div class="app-list-item">
            <div class="app-list-icon">
              <ion-icon name="calendar-outline"></ion-icon>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">Member Since</div>
              <div class="app-list-subtitle">{{ user.memberSince | date:'MMM yyyy' }}</div>
            </div>
          </div>

          <!-- Department -->
          <div class="app-list-item">
            <div class="app-list-icon">
              <ion-icon name="business-outline"></ion-icon>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">Department</div>
              <div class="app-list-subtitle">{{ user.department || 'Not specified' }}</div>
            </div>
            <ion-button fill="clear" size="small" (click)="editField('department')">
              <ion-icon name="create-outline" slot="icon-only" class="text-gray-400"></ion-icon>
            </ion-button>
          </div>
        </div>

        <!-- Edit Profile Button -->
        <ion-button expand="block" class="app-button app-button-outline" (click)="openEditModal()" [disabled]="isLoadingProfile">
          <div class="app-button-content">
            <ion-icon name="create-outline" class="app-button-icon"></ion-icon>
            <span>{{ isLoadingProfile ? 'Loading...' : 'Edit Profile' }}</span>
          </div>
        </ion-button>
      </div>

      <!-- Quick Actions -->
      <div class="app-card app-card-animated mt-lg">
        <h3 class="text-xl font-semibold text-gray-800 mb-lg">Quick Actions</h3>
        <div class="flex flex-col gap-sm">
          <!-- Activity -->
          <div class="app-list-item" (click)="viewActivity()">
            <div class="app-list-icon">
              <ion-icon name="pulse-outline"></ion-icon>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">Activity</div>
              <div class="app-list-subtitle">View your recent activity</div>
            </div>
            <ion-icon name="chevron-forward-outline" class="app-list-action"></ion-icon>
          </div>

          <!-- Notifications -->
          <div class="app-list-item" (click)="viewNotifications()">
            <div class="app-list-icon relative">
              <ion-icon name="notifications-outline"></ion-icon>
              <div class="absolute -top-1 -right-1 w-5 h-5 bg-danger rounded-full flex items-center justify-center">
                <span class="text-xs text-white font-semibold">3</span>
              </div>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">Notifications</div>
              <div class="app-list-subtitle">Manage your notifications</div>
            </div>
            <ion-icon name="chevron-forward-outline" class="app-list-action"></ion-icon>
          </div>

          <!-- Security -->
          <div class="app-list-item" (click)="viewSecurity()">
            <div class="app-list-icon">
              <ion-icon name="lock-closed-outline"></ion-icon>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">Security</div>
              <div class="app-list-subtitle">Password & security settings</div>
            </div>
            <ion-icon name="chevron-forward-outline" class="app-list-action"></ion-icon>
          </div>
        </div>
      </div>

      <!-- Logout Button -->
      <ion-button expand="block" class="app-button app-button-danger mt-lg" (click)="logout()">
        <div class="app-button-content">
          <ion-icon name="log-out-outline" class="app-button-icon"></ion-icon>
          <span>Sign Out</span>
        </div>
      </ion-button>
    </div>
  </div>

  <!-- Edit Profile Modal -->
  <ion-modal #editModal>
    <ng-template>
      <ion-header class="ion-no-border">
        <ion-toolbar>
          <ion-title>Edit Profile</ion-title>
          <ion-buttons slot="end">
            <ion-button fill="clear" (click)="closeEditModal()">
              <ion-icon name="close-outline" slot="icon-only"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content class="ion-padding">
        <form [formGroup]="profileForm" (ngSubmit)="saveProfile()" novalidate>

          <!-- Username Field -->
          <ion-item>
            <ion-input
              label="Username *"
              labelPlacement="stacked"
              type="text"
              formControlName="username"
              placeholder="Enter username"
              [class.ion-invalid]="hasFieldError('username')"
              [errorText]="getFieldError('username')">
            </ion-input>
          </ion-item>

          <!-- Full Name Field -->
          <ion-item>
            <ion-input
              label="Full Name *"
              labelPlacement="stacked"
              type="text"
              formControlName="fullName"
              placeholder="Enter your full name"
              [class.ion-invalid]="hasFieldError('fullName')"
              [errorText]="getFieldError('fullName')">
            </ion-input>
          </ion-item>

          <!-- Email Field -->
          <ion-item>
            <ion-input
              label="Email Address *"
              labelPlacement="stacked"
              type="email"
              formControlName="email"
              placeholder="Enter your email address"
              [class.ion-invalid]="hasFieldError('email')"
              [errorText]="getFieldError('email')">
            </ion-input>
          </ion-item>

          <!-- Phone Code Field -->
          <ion-item>
            <ion-input
              label="Phone Code"
              labelPlacement="stacked"
              type="text"
              formControlName="phoneCode"
              placeholder="e.g., +1"
              [class.ion-invalid]="hasFieldError('phoneCode')"
              [errorText]="getFieldError('phoneCode')">
            </ion-input>
          </ion-item>

          <!-- Phone Field -->
          <ion-item>
            <ion-input
              label="Phone Number"
              labelPlacement="stacked"
              type="tel"
              formControlName="phone"
              placeholder="Enter your phone number"
              [class.ion-invalid]="hasFieldError('phone')"
              [errorText]="getFieldError('phone')">
            </ion-input>
          </ion-item>

          <!-- Status Field -->
          <ion-item>
            <ion-select
              label="Status"
              labelPlacement="stacked"
              formControlName="status"
              placeholder="Select status">
              <ion-select-option *ngFor="let status of userStatusOptions" [value]="status.value">
                {{ status.label }}
              </ion-select-option>
            </ion-select>
          </ion-item>

          <!-- Password Field (Optional) -->
          <ion-item>
            <ion-input
              label="New Password (Optional)"
              labelPlacement="stacked"
              type="password"
              formControlName="password"
              placeholder="Leave blank to keep current password"
              [class.ion-invalid]="hasFieldError('password')"
              [errorText]="getFieldError('password')">
            </ion-input>
          </ion-item>

          <!-- Roles Display (Read-only) -->
          <ion-item *ngIf="currentUser?.roles?.length">
            <ion-label>
              <h3>Current Roles</h3>
              <p>{{ getRolesDisplayString() }}</p>
              <p class="ion-text-wrap">
                <small>Roles cannot be changed here. Contact your administrator for role updates.</small>
              </p>
            </ion-label>
          </ion-item>

          <!-- Action Buttons -->
          <div class="ion-padding-top">
            <ion-button
              expand="block"
              type="submit"
              [disabled]="profileForm.invalid || isSaving">
              <ion-icon *ngIf="!isSaving" name="save-outline" slot="start"></ion-icon>
              {{ isSaving ? 'Saving...' : 'Save Changes' }}
            </ion-button>

            <ion-button
              expand="block"
              fill="outline"
              (click)="closeEditModal()"
              [disabled]="isSaving">
              <ion-icon name="close-outline" slot="start"></ion-icon>
              Cancel
            </ion-button>
          </div>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
