<ion-content [fullscreen]="true" class="app-page has-gradient-bg">
  <!-- Background Elements -->
  <div class="app-background">
    <div class="app-floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>

  <!-- Main Login Container -->
  <div class="app-container centered">
    <div class="app-content">
      <!-- Logo/Brand Section -->
      <div class="text-center mb-xl">
        <div class="mb-lg">
          <ion-icon name="shield-checkmark" class="text-4xl text-white" style="font-size: 4rem; filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2)); animation: pulse 2s infinite;"></ion-icon>
        </div>
        <h1 class="text-4xl font-bold text-white m-0 mb-sm" style="text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); letter-spacing: -0.025em;">Welcome Back</h1>
        <p class="text-lg text-white m-0 font-normal" style="color: rgba(255, 255, 255, 0.9);">Sign in to your account to continue</p>
      </div>

      <!-- Login Form Card -->
      <div class="app-card">
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" novalidate>
          <!-- Username Field -->
          <div class="app-form-group">
            <label class="app-form-label">Username</label>
            <div class="app-input-container" [class.error]="isSubmitted && errorControl?.['username'].errors" [class.focused]="usernameFocused">
              <ion-icon name="person-outline" class="app-input-icon"></ion-icon>
              <ion-input
                type="text"
                formControlName="username"
                autocomplete="username"
                placeholder="Enter your username"
                class="app-input"
                (ionFocus)="usernameFocused = true"
                (ionBlur)="usernameFocused = false">
              </ion-input>
            </div>
            <div *ngIf="isSubmitted && errorControl?.['username'].errors" class="app-error-text">
              <span *ngIf="errorControl?.['username'].errors?.['required']">
                Username is required
              </span>
            </div>
          </div>

          <!-- Password Field -->
          <div class="app-form-group">
            <label class="app-form-label">Password</label>
            <div class="app-input-container" [class.error]="isSubmitted && errorControl?.['password'].errors" [class.focused]="passwordFocused">
              <ion-icon name="lock-closed-outline" class="app-input-icon"></ion-icon>
              <ion-input
                [type]="showPassword ? 'text' : 'password'"
                formControlName="password"
                autocomplete="current-password"
                placeholder="Enter your password"
                class="app-input"
                (ionFocus)="passwordFocused = true"
                (ionBlur)="passwordFocused = false">
              </ion-input>
              <button type="button" class="app-input-action" (click)="togglePassword()">
                <ion-icon [name]="showPassword ? 'eye-off-outline' : 'eye-outline'"></ion-icon>
              </button>
            </div>
            <div *ngIf="isSubmitted && errorControl?.['password'].errors" class="app-error-text">
              <span *ngIf="errorControl?.['password'].errors?.['required']">
                Password is required
              </span>
              <span *ngIf="errorControl?.['password'].errors?.['minlength']">
                Password must be at least 6 characters long
              </span>
            </div>
          </div>

          <!-- Forgot Password Link -->
          <div class="text-right mb-lg">
            <a href="#" class="text-primary text-sm font-medium" style="text-decoration: none; transition: color var(--app-transition-normal);">Forgot your password?</a>
          </div>

          <!-- Login Button -->
          <ion-button
            expand="block"
            type="submit"
            class="app-button mt-md"
            [disabled]="isLoading">
            <div class="app-button-content">
              <ion-spinner *ngIf="isLoading" name="crescent" class="app-button-spinner"></ion-spinner>
              <ion-icon *ngIf="!isLoading" name="log-in-outline" class="app-button-icon"></ion-icon>
              <span>{{ isLoading ? 'Signing in...' : 'Sign In' }}</span>
            </div>
          </ion-button>
        </form>

        <!-- Divider -->
        <div class="app-divider">
          <span class="app-divider-text">or</span>
        </div>

        <!-- Sign Up Link -->
        <div class="text-center">
          <p class="text-gray-600 text-sm m-0">
            Don't have an account?
            <a [routerLink]="['/signup']" class="text-primary font-semibold ml-xs" style="text-decoration: none; transition: color var(--app-transition-normal);">Create one</a>
          </p>
        </div>
      </div>
    </div>
  </div>
</ion-content>
