import { createReducer, on } from '@ngrx/store';
import { createEntityAdapter, EntityAdapter, EntityState } from '@ngrx/entity';
import { Role } from '../../models/role.model';
import * as RoleActions from './role.actions';

export interface RoleState extends EntityState<Role> {
  selectedRoleId: string | null;
  loading: boolean;
  error: any;
}

export const adapter: EntityAdapter<Role> = createEntityAdapter<Role>({
  selectId: (role: Role) => role.uuid,
  sortComparer: (a: Role, b: Role) => {
    // Sort by displayName alphabetically
    return a.displayName.localeCompare(b.displayName);
  },
});

export const initialState: RoleState = adapter.getInitialState({
  selectedRoleId: null,
  loading: false,
  error: null
});

export const roleReducer = createReducer(
  initialState,

  // Load Roles
  on(RoleActions.loadRoles, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RoleActions.loadRolesSuccess, (state, { roles }) =>
    adapter.setAll(roles, {
      ...state,
      loading: false
    })
  ),

  on(RoleActions.loadRolesFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Create Role
  on(RoleActions.createRole, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RoleActions.createRoleSuccess, (state, { role }) =>
    adapter.addOne(role, {
      ...state,
      loading: false
    })
  ),

  on(RoleActions.createRoleFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Update Role
  on(RoleActions.updateRole, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RoleActions.updateRoleSuccess, (state, { role }) =>
    adapter.updateOne(
      { id: role.uuid, changes: role },
      {
        ...state,
        loading: false
      }
    )
  ),

  on(RoleActions.updateRoleFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Clear Error
  on(RoleActions.clearRoleError, (state) => ({
    ...state,
    error: null
  }))
);

// Export the entity selectors
export const {
  selectIds,
  selectEntities,
  selectAll,
  selectTotal,
} = adapter.getSelectors();
