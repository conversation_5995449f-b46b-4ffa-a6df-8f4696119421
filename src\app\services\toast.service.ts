import { Injectable } from '@angular/core';
import { ToastController } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class ToastService {

  constructor(private toastController: ToastController) { }

  // Method for showing a success notification
  async showSuccess(message: string): Promise<void> {
    console.log(`Toast [SUCCESS]: ${message}`);
    const toast = await this.toastController.create({
      message: message,
      duration: 2000,
      color: 'success',
      position: 'top',
      icon: 'checkmark-circle-outline'
    });
    toast.present();
  }

  // Method for showing an error notification
  async showError(message: string): Promise<void> {
    console.error(`Toast [ERROR]: ${message}`);
    const toast = await this.toastController.create({
      message: message,
      duration: 3000,
      color: 'danger',
      position: 'top',
      icon: 'alert-circle-outline'
    });
    toast.present();
  }

  // Method for showing an info notification
  async showInfo(message: string): Promise<void> {
    console.log(`Toast [INFO]: ${message}`);
    const toast = await this.toastController.create({
      message: message,
      duration: 2500,
      color: 'primary',
      position: 'top',
      icon: 'information-circle-outline'
    });
    toast.present();
  }

  // Method for showing a warning notification
  async showWarning(message: string): Promise<void> {
    console.warn(`Toast [WARNING]: ${message}`);
    const toast = await this.toastController.create({
      message: message,
      duration: 2500,
      color: 'warning',
      position: 'top',
      icon: 'warning-outline'
    });
    toast.present();
  }
}
