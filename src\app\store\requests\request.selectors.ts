import {createFeatureSelector, createSelector} from '@ngrx/store';
import {RequestState, selectAll, selectEntities} from './request.reducer';
import {AppState} from '../index';

export const selectRequestState = createFeatureSelector<AppState, RequestState>('requests');

export const selectAllRequests = createSelector(
  selectRequestState,
  selectAll
);

export const selectRequestEntities = createSelector(
  selectRequestState,
  selectEntities
);

export const selectRequestLoading = createSelector(
  selectRequestState,
  (state: RequestState) => state.loading
);

export const selectRequestError = createSelector(
  selectRequestState,
  (state: RequestState) => state.error
);

export const selectSelectedRequestId = createSelector(
  selectRequestState,
  (state: RequestState) => state.selectedRequestId
);

export const selectSelectedRequest = createSelector(
  selectRequestEntities,
  selectSelectedRequestId,
  (entities, selectedId) => selectedId ? entities[selectedId] : null
);

export const selectRequestById = (id: string) => createSelector(
  selectRequestEntities,
  (entities) => entities[id] || null
);

export const selectPendingRequests = createSelector(
  selectAllRequests,
  (requests) => requests.filter(request => request.status === 'PENDING')
);

export const selectApprovedRequests = createSelector(
  selectAllRequests,
  (requests) => requests.filter(request => request.status === 'APPROVED')
);

export const selectRejectedRequests = createSelector(
  selectAllRequests,
  (requests) => requests.filter(request => request.status === 'REJECTED')
);

export const selectCompletedRequests = createSelector(
  selectAllRequests,
  (requests) => requests.filter(request => request.status === 'COMPLETED')
);

export const selectRequestsByStatus = (status: string) => createSelector(
  selectAllRequests,
  (requests) => requests.filter(request => request.status === status)
);

export const selectRequestStats = createSelector(
  selectAllRequests,
  (requests) => {
    return {
      pendingRequests: requests.filter(r => r.status === 'PENDING').length,
      approvedRequests: requests.filter(r => r.status === 'APPROVED').length,
      rejectedRequests: requests.filter(r => r.status === 'REJECTED').length,
      completedRequests: requests.filter(r => r.status === 'COMPLETED').length,
      totalRequests: requests.length
    };
  }
);
