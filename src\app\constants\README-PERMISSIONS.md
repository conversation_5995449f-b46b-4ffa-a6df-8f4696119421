# Permission System Documentation

## Overview
This application uses ngx-permissions to implement role-based access control (RBAC). The permission system controls access to menu items, UI components, and routes throughout the application.

## Permission Constants
All permissions are defined in `src/app/constants/permissions.ts` to ensure consistency and avoid typos.

### Available Permissions

#### Administrative Permissions
- `ADMIN` - Full administrative access to all features
- `USER_MANAGEMENT` - Can manage users (create, edit, delete, activate/deactivate)
- `ROLE_MANAGEMENT` - Can manage roles and permissions
- `SETTINGS_MANAGEMENT` - Can manage system settings

#### Request Management
- `REQUEST_CREATE` - Can create new requests
- `REQUEST_EDIT` - Can edit own requests
- `REQUEST_DELETE` - Can delete requests
- `REQUEST_VIEW` - Can view own requests
- `REQUEST_VIEW_ALL` - Can view all requests in the system
- `REQUEST_MANAGEMENT` - Full request management capabilities

#### Approval Management
- `APPROVAL_VIEW` - Can view approvals
- `APPROVAL_PROCESS` - Can approve/reject requests
- `APPROVAL_MANAGEMENT` - Full approval management capabilities
- `APPROVAL_VIEW_ALL` - Can view all approvals

#### Other Permissions
- `DASHBOARD_VIEW` - Can access dashboard
- `PROFILE_VIEW` - Can view profile
- `PROFILE_EDIT` - Can edit own profile
- `EXPENSE_CREATE` - Can create expense reports
- `EXPENSE_VIEW` - Can view expenses

## Usage Examples

### 1. Menu Item Guards
Menu items are automatically hidden based on permissions defined in `MENU_PERMISSIONS`:

```typescript
// In menu.page.ts
public appPages = [
  { 
    title: 'Users', 
    url: '/menu/users', 
    icon: 'people',
    permissions: MENU_PERMISSIONS.USERS // ['USER_MANAGEMENT', 'ADMIN']
  }
];
```

```html
<!-- In menu.page.html -->
<ion-item *ngxPermissionsOnly="p.permissions" [routerLink]="p.url">
  <ion-icon slot="start" [name]="p.icon"></ion-icon>
  <ion-label>{{ p.title }}</ion-label>
</ion-item>
```

### 2. Component-Level Guards
Hide buttons, sections, or entire components based on permissions:

```html
<!-- Hide admin-only buttons -->
<ion-button 
  *ngxPermissionsOnly="['ADMIN', 'USER_MANAGEMENT']"
  (click)="openCreateModal()">
  Add User
</ion-button>

<!-- Hide sections for non-managers -->
<div *ngxPermissionsOnly="['ADMIN', 'APPROVAL_MANAGEMENT']">
  <h3>Admin Controls</h3>
  <!-- Admin-only content -->
</div>
```

### 3. Route Guards
Protect entire routes using guards:

```typescript
// In app.routes.ts
{
  path: 'users',
  loadComponent: () => import('./pages/users/users.page').then(m => m.UsersPage),
  canActivate: [adminGuard] // Only admins can access
},
{
  path: 'approvals',
  loadComponent: () => import('./pages/approvals/approvals.page').then(m => m.ApprovalsPage),
  canActivate: [managerGuard] // Managers and admins can access
}
```

### 4. Using Permission Service
For complex permission logic in components:

```typescript
import { PermissionService } from '../services/permission.service';

constructor(private permissionService: PermissionService) {}

// Check specific permissions
canEditUser(): boolean {
  return this.permissionService.canManageUsers();
}

canApproveRequest(): boolean {
  return this.permissionService.canApproveRequests();
}

// Check multiple permissions
hasAnyAdminPermission(): boolean {
  return this.permissionService.hasAnyPermission(['ADMIN', 'USER_MANAGEMENT']);
}
```

## Permission Groups
Pre-defined permission groups for common roles:

- **ADMIN**: Full access to everything
- **MANAGER**: Can manage requests, approvals, and view dashboards
- **USER**: Basic access to create requests, view own data, edit profile

## Adding New Permissions

1. **Add to constants**: Update `PERMISSIONS` object in `permissions.ts`
2. **Update menu permissions**: Add to `MENU_PERMISSIONS` if needed
3. **Update permission service**: Add helper methods if needed
4. **Apply to components**: Use `*ngxPermissionsOnly` directive
5. **Update guards**: Modify existing guards or create new ones

## Best Practices

1. **Use constants**: Always use `PERMISSIONS.PERMISSION_NAME` instead of strings
2. **Principle of least privilege**: Give users only the permissions they need
3. **Consistent naming**: Follow the pattern `FEATURE_ACTION` (e.g., `USER_CREATE`)
4. **Document changes**: Update this file when adding new permissions
5. **Test thoroughly**: Verify permissions work correctly for all user roles

## Security Notes

- Permissions are enforced on the frontend for UX, but **must also be enforced on the backend**
- Route guards provide an additional layer of security
- Always validate permissions on the server side for sensitive operations
- Permissions are loaded from the `/me` endpoint and stored in ngx-permissions service
