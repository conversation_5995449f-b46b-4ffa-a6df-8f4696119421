import { createAction, props } from '@ngrx/store';
import { Role, RoleDto, Permission } from '../../models/role.model';

// Load Roles
export const loadRoles = createAction(
  '[Role] Load Roles'
);

export const loadRolesSuccess = createAction(
  '[Role] Load Roles Success',
  props<{ roles: Role[] }>()
);

export const loadRolesFailure = createAction(
  '[Role] Load Roles Failure',
  props<{ error: any }>()
);

// Create Role
export const createRole = createAction(
  '[Role] Create Role',
  props<{ roleDto: RoleDto }>()
);

export const createRoleSuccess = createAction(
  '[Role] Create Role Success',
  props<{ role: Role }>()
);

export const createRoleFailure = createAction(
  '[Role] Create Role Failure',
  props<{ error: any }>()
);

// Update Role
export const updateRole = createAction(
  '[Role] Update Role',
  props<{ roleDto: RoleDto }>()
);

export const updateRoleSuccess = createAction(
  '[Role] Update Role Success',
  props<{ role: Role }>()
);

export const updateRoleFailure = createAction(
  '[Role] Update Role Failure',
  props<{ error: any }>()
);

// Clear Role Error
export const clearRoleError = createAction(
  '[Role] Clear Role Error'
);

// Load Permissions
export const loadPermissions = createAction(
  '[Role] Load Permissions'
);

export const loadPermissionsSuccess = createAction(
  '[Role] Load Permissions Success',
  props<{ permissions: Permission[] }>()
);

export const loadPermissionsFailure = createAction(
  '[Role] Load Permissions Failure',
  props<{ error: any }>()
);
