# Global List Pages Styling Guide

This guide explains how to use the global list pages styling system for creating consistent GraphQL-powered list views across the application.

## Overview

The global list pages styling is located in `src/theme/list-pages.scss` and provides a consistent design system for all list-based pages like users, approvals, requests, etc.

## Basic Structure

To use the global list styling, follow this HTML structure:

```html
<ion-content class="app-page has-gradient-bg list-page">
  <!-- Background -->
  <div class="app-background">
    <div class="app-floating-shapes"></div>
  </div>

  <!-- Main Content -->
  <div class="app-container">
    <div class="app-content app-content-wide">
      
      <!-- Header -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">Your Page Title</h1>
          <p class="page-subtitle">Your page description</p>
        </div>
        
        <!-- Optional Action Button -->
        <div class="header-actions">
          <ion-button class="app-button" (click)="yourAction()">
            <div class="app-button-content">
              <ion-icon name="add-outline" class="app-button-icon"></ion-icon>
              <span>Add Item</span>
            </div>
          </ion-button>
        </div>
      </div>

      <!-- List Cards Container -->
      <div class="list-cards">
        <!-- Search and Filters Card -->
        <div class="app-card app-card-animated search-filters-card">
          <div class="search-filters">
            <!-- Search Bar -->
            <div class="search-container">
              <ion-searchbar
                [(ngModel)]="searchQuery"
                (ionInput)="onSearchInput($event)"
                placeholder="Search items..."
                show-clear-button="focus"
                class="app-searchbar">
              </ion-searchbar>
            </div>

            <!-- Filters -->
            <div class="filters-container">
              <div class="filter-chips">
                <!-- Your filter chips here -->
              </div>

              <div class="filter-toggles">
                <!-- Your toggle filters here -->
              </div>
            </div>
          </div>
        </div>

        <!-- List Content Card -->
        <div class="app-card app-card-animated list-content-card">
          <!-- Your list content here -->
        </div>
      </div>
    </div>
  </div>
</ion-content>
```

## Key Classes

### Main Container Classes
- `list-page` - Apply to `ion-content` to enable list page styling
- `list-cards` - Container for the search/filters and list content cards
- `search-filters-card` - Apply to the search and filters card
- `list-content-card` - Apply to the main list content card

### List Content Classes
- `list-items` - Apply to `ion-list` for consistent item spacing
- `list-item` - Apply to each `ion-item` for proper spacing and hover effects
- `empty-state` - For empty state messaging
- `loading-container` - For loading state content

### Infinite Scroll Classes
- `infinite-scroll` - Apply to `ion-infinite-scroll`
- `infinite-scroll-content` - Apply to `ion-infinite-scroll-content`
- `loading-content` - Container for loading spinner and text
- `loading-spinner` - Apply to the spinner
- `loading-text` - Apply to loading text

### End of List Classes
- `end-of-list` - Container for end of list message
- `end-message` - The actual message container
- `end-icon` - Icon for end of list

## Spacing

The global styling provides consistent spacing:
- **Card Gap**: `var(--app-spacing-xl)` between search/filters and list cards
- **Item Gap**: `var(--app-spacing-md)` between list items
- **Header Margin**: `var(--app-spacing-2xl)` below page header

## Responsive Design

The styling includes responsive breakpoints:
- Mobile: Stacked layout, smaller spacing
- Tablet/Desktop: Side-by-side filters, larger spacing

## Customization

For page-specific styling, create a separate SCSS file for your page and only include item-specific styles. The global styling handles all layout, spacing, and common elements.

Example for a custom item type:

```scss
// your-page.page.scss
.your-item {
  .item-specific-content {
    // Your custom styling here
  }
}
```

## Benefits

1. **Consistency**: All list pages look and feel the same
2. **Maintainability**: Changes to spacing/layout affect all pages
3. **Rapid Development**: New list pages can be created quickly
4. **Responsive**: Built-in mobile and desktop layouts
5. **Accessibility**: Consistent focus states and interactions
