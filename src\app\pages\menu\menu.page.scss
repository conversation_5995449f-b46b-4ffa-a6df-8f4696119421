// Custom styles for menu to match dashboard aesthetic

:host {
  // Typography
  --ion-font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;

  // Menu styling
  --ion-background-color: #f5f7fa;
  --ion-item-background: transparent;
  --ion-item-color: #374151;
  --ion-text-color: #374151;

  // Menu header
  --ion-toolbar-background: rgba(255, 255, 255, 0.8);
  --ion-toolbar-border-color: transparent;
}

// Menu header styling
ion-menu ion-header {
  &::after {
    display: none;
  }

  ion-toolbar {
    --background: linear-gradient(to right, #f0f4ff, #f5f3ff);
    --border-color: transparent;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(8px);

    ion-title {
      font-weight: 600;
      font-size: 18px;
      color: #374151;
    }
  }
}

// Menu content styling
ion-menu ion-content {
  --background: linear-gradient(135deg, #f5f7fa 0%, #eef2f6 100%);

  ion-list {
    background: transparent;
    padding-top: 16px;

    &::part(native) {
      background: transparent;
    }
  }

  ion-item {
    --background: transparent;
    --background-hover: rgba(0, 0, 0, 0.04);
    --background-activated: rgba(0, 0, 0, 0.06);
    --background-focused: rgba(0, 0, 0, 0.06);
    --border-color: transparent;
    --padding-start: 16px;
    --padding-end: 16px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    --border-radius: 8px;
    margin: 4px 8px;

    &::part(native) {
      border-radius: 8px;
    }

    ion-icon {
      color: #6b7280;
      margin-right: 12px;
    }

    ion-label {
      font-weight: 500;
      font-size: 15px;
      color: #374151;
    }

    &.active-item {
      --background: rgba(59, 130, 246, 0.08);

      ion-icon {
        color: #3880ff;
      }

      ion-label {
        color: #3880ff;
        font-weight: 600;
      }
    }
  }
}

// Active menu item
ion-item.active-item {
  --background: rgba(59, 130, 246, 0.08);

  ion-icon {
    color: #3880ff;
  }

  ion-label {
    color: #3880ff;
    font-weight: 600;
  }
}