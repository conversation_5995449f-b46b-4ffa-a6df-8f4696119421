import {Injectable} from '@angular/core';
import {Apollo} from 'apollo-angular';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {Request} from '../models/request.model';
import {User} from '../models/user.model';

// GraphQL Queries and Mutations
import {
  APPROVE_REQUEST,
  CREATE_REQUEST,
  DELETE_REQUEST,
  GET_ALL_REQUESTS,
  GET_MY_REQUESTS,
  UPDATE_REQUEST,
  SAVE_REQUEST
} from './graphql/request.graphql';

import {
  LIST_USERS,
  GET_USER_BY_UUID,
  SAVE_USER,
  DELETE_USER,
  DEACTIVATE_USER,
  ACTIVATE_USER,
  ASSIGN_ROLES_TO_USER
} from './graphql/user.graphql';

@Injectable({
  providedIn: 'root'
})
export class GraphqlService {

  constructor(private apollo: Apollo) { }

  // Request queries
  getMyRequests(filter?: any): Observable<any> {
    console.log('GraphQL Service getMyRequests called');
    return this.apollo.watchQuery<any>({
      query: GET_MY_REQUESTS
    }).valueChanges.pipe(
      map(result => {
        console.log('Apollo result:', result);
        console.log('Apollo result.data:', result.data);
        console.log('Apollo result.data.getMyRequests:', result.data.getMyRequests);
        return result.data.getMyRequests;
      })
    );
  }

  getAllRequests(filter?: any): Observable<any> {
    return this.apollo.watchQuery<any>({
      query: GET_ALL_REQUESTS
    }).valueChanges.pipe(
      map(result => result.data.getAllRequests)
    );
  }

  // Request mutations
  createRequest(requestDto: any): Observable<any> {
    return this.apollo.mutate<any>({
      mutation: CREATE_REQUEST,
      variables: {
        title: requestDto.title,
        description: requestDto.description
      },
      refetchQueries: [{ query: GET_MY_REQUESTS }]
    }).pipe(
      map(result => result.data!.createRequest)
    );
  }

  updateRequest(requestDto: any): Observable<any> {
    return this.apollo.mutate<any>({
      mutation: UPDATE_REQUEST,
      variables: {
        uuid: requestDto.uuid,
        title: requestDto.title,
        description: requestDto.description
      },
      refetchQueries: [{ query: GET_MY_REQUESTS }]
    }).pipe(
      map(result => result.data!.updateRequest)
    );
  }

  // User mutations
  saveUser(userDto: any): Observable<any> {
    return this.apollo.mutate<any>({
      mutation: SAVE_USER,
      variables: { userDto },
      refetchQueries: [{ query: LIST_USERS }]
    }).pipe(
      map(result => result.data!.saveUser)
    );
  }

  saveRequest(requestDto: any): Observable<any> {
    return this.apollo.mutate<any>({
      mutation: SAVE_REQUEST,
      variables: {
        title: requestDto.title,
        description: requestDto.description
      },
      refetchQueries: [{ query: GET_MY_REQUESTS }]
    }).pipe(
      map(result => result.data!.saveRequest)
    );
  }

  approveRequest(approvalDto: any): Observable<any> {
    return this.apollo.mutate<any>({
      mutation: APPROVE_REQUEST,
      variables: {
        requestUuid: approvalDto.requestUuid,
        decision: approvalDto.decision,
        comment: approvalDto.comment
      },
      refetchQueries: [{ query: GET_MY_REQUESTS }, { query: GET_ALL_REQUESTS }]
    }).pipe(
      map(result => result.data!.approveRequest)
    );
  }

  deleteRequest(uuid: string): Observable<any> {
    return this.apollo.mutate<any>({
      mutation: DELETE_REQUEST,
      variables: { uuid },
      refetchQueries: [{ query: GET_MY_REQUESTS }, { query: GET_ALL_REQUESTS }]
    }).pipe(
      map(result => result.data!.deleteRequest)
    );
  }
}
