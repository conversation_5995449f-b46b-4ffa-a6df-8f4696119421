<ion-content class="app-page has-gradient-bg list-page">
  <!-- Background -->
  <div class="app-background">
    <div class="app-floating-shapes"></div>
  </div>

  <!-- Main Content -->
  <div class="app-container">
    <div class="app-content app-content-wide">

      <!-- Header -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">Role Management</h1>
          <p class="page-subtitle">Manage system roles and permissions</p>
        </div>

        <!-- Add Role Button (Admin Only) -->
        <div class="header-actions">
          <ion-button
            *ngxPermissionsOnly="['ROLE_ROLES_ADD', 'ROLE_ROLES_VIEW']"
            class="app-button"
            (click)="openCreateModal()"
            id="add-role-trigger">
            <div class="app-button-content">
              <ion-icon name="add-outline" class="app-button-icon"></ion-icon>
              <span>Add Role</span>
            </div>
          </ion-button>
        </div>
      </div>

      <!-- List Cards Container -->
      <div class="list-cards">
        <!-- Search and Filters Card -->
        <div class="app-card app-card-animated search-filters-card">
          <div class="search-filters">
            <!-- Search Bar -->
            <div class="search-container">
              <ion-searchbar
                [(ngModel)]="searchTerm"
                (ionInput)="onSearchChange($event)"
                placeholder="Search roles by name..."
                show-clear-button="focus"
                class="app-searchbar">
              </ion-searchbar>
            </div>
          </div>
        </div>

        <!-- Roles List Card -->
        <div class="app-card app-card-animated list-content-card">
        <!-- Pull to Refresh -->
        <ion-refresher slot="fixed" (ionRefresh)="onRefresh($event)">
          <ion-refresher-content></ion-refresher-content>
        </ion-refresher>

        <!-- Loading State -->
        <div *ngIf="loading" class="loading-container">
          <div class="skeleton-list">
            <div *ngFor="let item of [1,2,3,4,5]" class="skeleton-item">
              <ion-skeleton-text animated style="width: 60px; height: 60px; border-radius: 50%;"></ion-skeleton-text>
              <div class="skeleton-content">
                <ion-skeleton-text animated style="width: 40%;"></ion-skeleton-text>
                <ion-skeleton-text animated style="width: 60%;"></ion-skeleton-text>
                <ion-skeleton-text animated style="width: 30%;"></ion-skeleton-text>
              </div>
            </div>
          </div>
        </div>

        <!-- Roles List -->
        <div *ngIf="!loading" class="roles-list">
          <div *ngIf="filteredRoles.length === 0" class="empty-state">
            <ion-icon name="business-outline" class="empty-icon"></ion-icon>
            <h3>No roles found</h3>
            <p>Try adjusting your search or create a new role</p>
          </div>

          <ion-list *ngIf="filteredRoles.length > 0" class="list-items" role="feed">
            <ion-item
              *ngFor="let role of filteredRoles; trackBy: trackByRoleId"
              class="list-item role-item"
              role="article"
              button>

              <!-- Role Icon -->
              <div slot="start" class="role-icon">
                <ion-icon name="business-outline"></ion-icon>
              </div>

              <!-- Role Info -->
              <ion-label class="role-info">
                <div class="role-name">
                  <h2>{{ role.displayName }}</h2>
                  <ion-badge
                    color="primary"
                    class="role-badge">
                    {{ role.name }}
                  </ion-badge>
                </div>

                <div class="role-details" *ngIf="role.createdAt">
                  <p class="role-created">
                    <ion-icon name="calendar-outline"></ion-icon>
                    Created {{ role.createdAt | date:'short' }}
                  </p>
                </div>
              </ion-label>

              <!-- Actions -->
              <div slot="end" class="role-actions">
                <ion-button
                  fill="clear"
                  size="small"
                  (click)="openEditModal(role); $event.stopPropagation()"
                  class="action-button">
                  <ion-icon name="create-outline" slot="icon-only"></ion-icon>
                </ion-button>
              </div>
            </ion-item>
          </ion-list>

          <!-- End of List Message -->
          <div *ngIf="filteredRoles.length > 0" class="end-of-list">
            <div class="end-message">
              <ion-icon name="checkmark-circle-outline" class="end-icon"></ion-icon>
              <p>You've reached the end of the list</p>
              <small>{{ filteredRoles.length }} roles loaded</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>

  <!-- Role Modal (Create/Edit) -->
  <ion-modal [isOpen]="isModalOpen" (didDismiss)="closeModal()">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-button (click)="closeModal()">Cancel</ion-button>
          </ion-buttons>
          <ion-title>{{ modalMode === 'create' ? 'Add New Role' : 'Edit Role' }}</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="saveRole()" [strong]="true" [disabled]="roleForm.invalid || loading">
              {{ loading ? 'Saving...' : (modalMode === 'create' ? 'Create' : 'Update') }}
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content class="ion-padding">
        <form [formGroup]="roleForm" (ngSubmit)="saveRole()">

          <!-- Display Name Field -->
          <ion-item>
            <ion-input
              label="Display Name *"
              labelPlacement="stacked"
              type="text"
              formControlName="displayName"
              placeholder="Enter role display name"
              maxlength="50"
              [class.ion-invalid]="roleForm.get('displayName')?.invalid && roleForm.get('displayName')?.touched"
              errorText="Display name is required (2-50 characters)">
            </ion-input>
          </ion-item>

          <!-- Permissions Section -->
          <div class="permissions-section mt-6">
            <ion-text>
              <h3 class="text-lg font-semibold mb-4">Permissions</h3>
            </ion-text>

            <!-- Loading state -->
            <div *ngIf="permissionsLoading$ | async" class="text-center py-4">
              <ion-text color="medium">Loading permissions...</ion-text>
            </div>

            <!-- Error state -->
            <div *ngIf="permissionsError$ | async as error" class="text-center py-4">
              <ion-text color="danger">Error loading permissions: {{ error }}</ion-text>
            </div>

            <!-- Permissions by Group -->
            <div *ngIf="!(permissionsLoading$ | async) && !(permissionsError$ | async)"
                 formArrayName="permissions">
              <div *ngFor="let group of permissionsByGroup | keyvalue; trackBy: trackByGroupName"
                   class="permission-group mb-6">

                <!-- Group Header -->
                <ion-text>
                  <h4 class="text-md font-medium mb-3 text-primary">{{ group.key }}</h4>
                </ion-text>

                <!-- Permissions in Group -->
                <div class="permission-items">
                  <ion-item *ngFor="let permission of group.value; let i = index; trackBy: trackByPermissionId"
                            lines="none" class="permission-item">
                    <ion-checkbox
                      slot="start"
                      [formControlName]="getPermissionIndex(permission.uuid)"
                      class="mr-3">
                    </ion-checkbox>
                    <ion-label class="ion-text-wrap">
                      <h3 class="font-medium">{{ permission.name }}</h3>
                      <p class="text-sm text-gray-600">{{ permission.description }}</p>
                    </ion-label>
                  </ion-item>
                </div>
              </div>
            </div>
          </div>

        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>