// Global Component Styles
// =============================================================================

@use './variables.scss' as *;
@use './mixins.scss' as *;

// Page Layout Components
// -----------------------------------------------------------------------------
.app-page {
  --background: transparent;

  &.has-gradient-bg {
    .app-background {
      @include gradient-background();

      .app-floating-shapes {
        @include floating-shapes();
      }
    }
  }
}

.app-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--app-z-background);
}

.app-container {
  @include page-container();

  &.centered {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.app-content {
  @include centered-content();
}

// Header Components
// -----------------------------------------------------------------------------
.app-header {
  @include page-header();

  .app-header-title {
    font-size: var(--app-font-size-3xl);
    font-weight: var(--app-font-weight-bold);
    color: var(--app-white);
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .app-header-button {
    @include glass-button(medium);
  }
}

// Card Components
// -----------------------------------------------------------------------------
.app-card {
  @include glass-card();
  @include fade-in-up();

  &.app-card-sm {
    @include glass-card(var(--app-spacing-lg));
  }

  &.app-card-lg {
    @include glass-card(var(--app-spacing-2xl));
  }

  &.app-card-animated {
    @include fade-in-up(0.2s);
  }
}

// Form Components
// -----------------------------------------------------------------------------
.app-form-group {
  margin-bottom: var(--app-spacing-lg);
}

.app-form-label {
  display: block;
  font-size: var(--app-font-size-sm);
  font-weight: var(--app-font-weight-semibold);
  color: var(--app-gray-700);
  margin-bottom: var(--app-spacing-sm);
  letter-spacing: 0.025em;
}

.app-input-container {
  position: relative;
  display: flex;
  align-items: center;
  @include modern-input();

  .app-input-icon {
    @include input-icon();
  }

  .app-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 0;
    font-size: var(--app-font-size-base);
    color: var(--app-gray-800);

    &::placeholder {
      color: var(--app-gray-400);
    }
  }

  .app-input-action {
    background: none;
    border: none;
    color: var(--app-gray-400);
    padding: var(--app-spacing-sm);
    margin-right: var(--app-spacing-sm);
    cursor: pointer;
    transition: color var(--app-transition-normal);

    &:hover {
      color: var(--app-gray-600);
    }
  }
}

.app-error-text {
  margin-top: var(--app-spacing-sm);
  font-size: var(--app-font-size-sm);
  color: var(--app-danger);
  font-weight: var(--app-font-weight-medium);
}

// Button Components
// -----------------------------------------------------------------------------
.app-button {
  @include modern-button();

  &.app-button-outline {
    @include modern-button(outline);
  }

  &.app-button-danger {
    @include modern-button(danger);
  }

  .app-button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--app-spacing-sm);
  }

  .app-button-icon {
    font-size: var(--app-font-size-xl);
  }

  .app-button-spinner {
    --color: currentColor;
    width: 1.25rem;
    height: 1.25rem;
  }
}

// Avatar Components
// -----------------------------------------------------------------------------
.app-avatar {
  position: relative;

  .app-avatar-image {
    width: 80px;
    height: 80px;
    border-radius: var(--app-radius-full);
    background: var(--app-gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 32px rgba(var(--app-primary-rgb), 0.3);
    border: 3px solid var(--app-glass-border);
  }

  .app-avatar-icon {
    font-size: 2.5rem;
    color: var(--app-white);
  }

  .app-avatar-edit {
    position: absolute;
    bottom: -4px;
    right: -4px;
    @include glass-button(small);
    background: var(--app-white);
    color: var(--app-primary);
    box-shadow: var(--app-shadow-md);

    ion-icon {
      font-size: 1rem;
    }
  }
}

// Badge Components
// -----------------------------------------------------------------------------
.app-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--app-radius-full);
  font-size: var(--app-font-size-xs);
  font-weight: var(--app-font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;

  &.app-badge-success {
    background: var(--app-success-light);
    color: var(--app-success);
  }

  &.app-badge-warning {
    background: var(--app-warning-light);
    color: var(--app-warning);
  }

  &.app-badge-danger {
    background: var(--app-danger-light);
    color: var(--app-danger);
  }

  &.app-badge-info {
    background: var(--app-info-light);
    color: var(--app-info);
  }

  &.app-badge-gray {
    background: rgba(var(--app-gray-500), 0.1);
    color: var(--app-gray-600);
  }
}

// List Components
// -----------------------------------------------------------------------------
.app-list-item {
  display: flex;
  align-items: center;
  padding: var(--app-spacing-md) 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all var(--app-transition-normal);
  cursor: pointer;

  &:hover {
    background: rgba(var(--app-primary-rgb), 0.02);
    border-radius: var(--app-radius-lg);
    padding-left: var(--app-spacing-sm);
    padding-right: var(--app-spacing-sm);
  }

  &:last-child {
    border-bottom: none;
  }

  .app-list-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--app-radius-lg);
    background: rgba(var(--app-primary-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--app-spacing-md);

    ion-icon {
      font-size: var(--app-font-size-xl);
      color: var(--app-primary);
    }
  }

  .app-list-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .app-list-title {
    font-size: var(--app-font-size-base);
    font-weight: var(--app-font-weight-semibold);
    color: var(--app-gray-800);
    margin-bottom: 0.25rem;
  }

  .app-list-subtitle {
    font-size: var(--app-font-size-sm);
    color: var(--app-gray-500);
    font-weight: var(--app-font-weight-normal);
  }

  .app-list-action {
    font-size: var(--app-font-size-xl);
    color: var(--app-gray-400);
    transition: all var(--app-transition-normal);
  }
}

.app-list-item:hover .app-list-action {
  color: var(--app-primary);
  transform: translateX(4px);
}

// Divider Components
// -----------------------------------------------------------------------------
.app-divider {
  position: relative;
  text-align: center;
  margin: var(--app-spacing-xl) 0;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--app-gray-200);
  }

  .app-divider-text {
    background: var(--app-glass-bg);
    color: var(--app-gray-400);
    padding: 0 var(--app-spacing-md);
    font-size: var(--app-font-size-sm);
    font-weight: var(--app-font-weight-medium);
  }
}

// Status Indicator Components
// -----------------------------------------------------------------------------
.app-status-indicator {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-sm);

  .app-status-dot {
    width: 8px;
    height: 8px;
    border-radius: var(--app-radius-full);

    &.active {
      background: var(--app-success);
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    }

    &.inactive {
      background: var(--app-gray-400);
    }
  }

  .app-status-text {
    font-size: var(--app-font-size-sm);
    font-weight: var(--app-font-weight-medium);

    &.active {
      color: var(--app-success);
    }

    &.inactive {
      color: var(--app-gray-500);
    }
  }
}
