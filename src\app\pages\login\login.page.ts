import {Component, OnInit, ViewEncapsulation} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {AuthService} from '../../services/auth/auth.service';
import {NotificationService} from '../../services/notification.service';
import {Router, RouterLink} from '@angular/router';
import {
  IonButton,
  IonContent,
  IonIcon,
  IonInput,
  IonSpinner
} from '@ionic/angular/standalone';
import {addIcons} from 'ionicons';
import {
  eyeOffOutline,
  eyeOutline,
  lockClosedOutline,
  logInOutline,
  personOutline,
  shieldCheckmarkOutline
} from 'ionicons/icons';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    IonContent,
    IonButton,
    IonIcon,
    IonInput,
    IonSpinner,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterLink
  ]
})
export class LoginPage implements OnInit {
  loginForm: FormGroup;
  isSubmitted = false;
  isLoading = false;
  showPassword = false;
  usernameFocused = false;
  passwordFocused = false;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private notificationService: NotificationService
  ) {
    addIcons({
      'person-outline': personOutline,
      'lock-closed-outline': lockClosedOutline,
      'log-in-outline': logInOutline,
      'eye-outline': eyeOutline,
      'eye-off-outline': eyeOffOutline,
      'shield-checkmark': shieldCheckmarkOutline
    });
  }

  ngOnInit() {
    this.loginForm = this.formBuilder.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  get errorControl() {
    return this.loginForm.controls;
  }

  togglePassword() {
    this.showPassword = !this.showPassword;
  }

  async onSubmit() {
    this.isSubmitted = true;
    if (this.loginForm.valid) {
      this.isLoading = true;
      try {
        const loginData = {
          username: this.loginForm.value.username,
          password: this.loginForm.value.password
        };

        // Login and wait for token to be stored
        await this.authService.login(loginData);

        // Now call /me endpoint - token should be available in interceptor
        // This will also persist permissions automatically
        const userData = await this.authService.me();

        // Verify permissions were loaded
        if (!userData.permissions || userData.permissions.length === 0) {
          throw new Error('No permissions received from server');
        }

        this.notificationService.showSuccess('Login successful!');
        this.router.navigate(['/menu/dashboard']);
      } catch (error: any) {
        console.error('Login failed:', error);
        const errorMessage = error?.error?.message || error?.message || 'Invalid username or password.';
        this.notificationService.showError(`Login failed: ${errorMessage}`);
      } finally {
        this.isLoading = false;
      }
    } else {
      console.log('Please provide all the required values!');
      this.notificationService.showError('Please fill in all required fields correctly.');
    }
  }
}
