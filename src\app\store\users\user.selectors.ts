import {createFeatureSelector, createSelector} from '@ngrx/store';
import {UserState, selectAll, selectEntities} from './user.reducer';
import {AppState} from '../index';
import {UserStatus} from '../../models/user.model';

export const selectUserState = createFeatureSelector<AppState, UserState>('users');

export const selectAllUsers = createSelector(
  selectUserState,
  selectAll
);

export const selectUserEntities = createSelector(
  selectUserState,
  selectEntities
);

export const selectUserLoading = createSelector(
  selectUserState,
  (state: UserState) => state.loading
);

export const selectUserError = createSelector(
  selectUserState,
  (state: UserState) => state.error
);

export const selectSelectedUserUuid = createSelector(
  selectUserState,
  (state: UserState) => state.selectedUserUuid
);

export const selectSelectedUser = createSelector(
  selectUserEntities,
  selectSelectedUserUuid,
  (entities, selectedUuid) => selectedUuid ? entities[selectedUuid] : null
);

export const selectUserByUuid = (uuid: string) => createSelector(
  selectUserEntities,
  (entities) => entities[uuid]
);

export const selectActiveUsers = createSelector(
  selectAllUsers,
  (users) => users.filter(user => user.status === UserStatus.ACTIVE)
);

export const selectInactiveUsers = createSelector(
  selectAllUsers,
  (users) => users.filter(user => user.status === UserStatus.INACTIVE)
);

export const selectUsersByStatus = (status: UserStatus) => createSelector(
  selectAllUsers,
  (users) => users.filter(user => user.status === status)
);

export const selectUsersByRole = (roleUuid: string) => createSelector(
  selectAllUsers,
  (users) => users.filter(user => user.roles.some(role => role.uuid === roleUuid))
);

export const selectUsersCount = createSelector(
  selectUserState,
  (state: UserState) => state.totalCount
);

export const selectActiveUsersCount = createSelector(
  selectActiveUsers,
  (users) => users.length
);

export const selectUserFilters = createSelector(
  selectUserState,
  (state: UserState) => state.filters
);

export const selectUserPagination = createSelector(
  selectUserState,
  (state: UserState) => ({
    currentPage: state.currentPage,
    totalPages: state.totalPages,
    pageSize: state.pageSize,
    totalCount: state.totalCount
  })
);

export const selectFilteredUsers = createSelector(
  selectAllUsers,
  selectUserFilters,
  (users, filters) => {
    let filteredUsers = users;

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredUsers = filteredUsers.filter(user =>
        user.username.toLowerCase().includes(searchTerm) ||
        user.fullName.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm)
      );
    }

    if (filters.status) {
      filteredUsers = filteredUsers.filter(user => user.status === filters.status);
    }

    if (filters.roleUuid) {
      filteredUsers = filteredUsers.filter(user =>
        user.roles.some(role => role.uuid === filters.roleUuid)
      );
    }

    return filteredUsers;
  }
);
