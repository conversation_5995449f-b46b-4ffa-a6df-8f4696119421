import { Injectable } from '@angular/core';
import { NgxPermissionsService } from 'ngx-permissions';
import { AuthService } from './auth/auth.service';

@Injectable({
  providedIn: 'root'
})
export class PermissionDebugService {

  constructor(
    private ngxPermissionsService: NgxPermissionsService,
    private authService: AuthService
  ) {}

  /**
   * Debug method to check current permission state
   */
  debugPermissionState(): void {
    console.group('🔐 Permission Debug Information');
    
    // Check authentication status
    console.log('🔑 Authentication Status:', this.authService.isAuthenticated());
    
    // Check if permissions are loaded
    console.log('📋 Permissions Loaded:', this.authService.hasPermissionsLoaded());
    
    // Get current permissions
    const permissions = this.ngxPermissionsService.getPermissions();
    const permissionKeys = Object.keys(permissions);
    console.log('🎯 Current Permissions Count:', permissionKeys.length);
    console.log('🎯 Current Permissions:', permissionKeys);
    
    // Check localStorage
    const storedPermissions = localStorage.getItem('userPermissions');
    if (storedPermissions) {
      try {
        const parsed = JSON.parse(storedPermissions);
        console.log('💾 Stored Permissions:', parsed);
      } catch (error) {
        console.error('💾 Error parsing stored permissions:', error);
      }
    } else {
      console.log('💾 No permissions in localStorage');
    }
    
    // Check token
    const token = this.authService.getToken();
    console.log('🎫 Token Present:', !!token);
    if (token) {
      console.log('🎫 Token Length:', token.length);
    }
    
    console.groupEnd();
  }

  /**
   * Test permission restoration
   */
  async testPermissionRestoration(): Promise<boolean> {
    console.log('🧪 Testing permission restoration...');
    
    // Clear current permissions
    this.ngxPermissionsService.flushPermissions();
    console.log('🧹 Cleared current permissions');
    
    // Try to restore
    const permissionsKey = 'userPermissions';
    const storedPermissions = localStorage.getItem(permissionsKey);
    
    if (storedPermissions) {
      try {
        const permissions = JSON.parse(storedPermissions);
        if (Array.isArray(permissions)) {
          const validPermissions = permissions.filter(p => p.startsWith('ROLE_'));
          this.ngxPermissionsService.loadPermissions(validPermissions);
          console.log('✅ Permissions restored successfully:', validPermissions);
          return true;
        }
      } catch (error) {
        console.error('❌ Error restoring permissions:', error);
      }
    }
    
    console.log('❌ No permissions to restore');
    return false;
  }

  /**
   * Simulate permission loss and restoration
   */
  async simulatePermissionLoss(): Promise<void> {
    console.log('🎭 Simulating permission loss...');
    
    // Store current state
    const currentPermissions = Object.keys(this.ngxPermissionsService.getPermissions());
    console.log('📸 Current permissions before simulation:', currentPermissions);
    
    // Clear permissions (simulating page refresh)
    this.ngxPermissionsService.flushPermissions();
    console.log('💥 Permissions cleared (simulating page refresh)');
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Try to restore
    const restored = await this.testPermissionRestoration();
    
    if (restored) {
      console.log('🎉 Permission restoration simulation successful!');
    } else {
      console.log('😞 Permission restoration simulation failed');
    }
  }

  /**
   * Check if specific permission exists
   */
  checkPermission(permission: string): boolean {
    const permissions = this.ngxPermissionsService.getPermissions();
    const hasPermission = !!permissions[permission];
    console.log(`🔍 Permission '${permission}':`, hasPermission ? '✅ GRANTED' : '❌ DENIED');
    return hasPermission;
  }

  /**
   * List all available permissions with their status
   */
  listAllPermissions(): void {
    const permissions = this.ngxPermissionsService.getPermissions();
    const permissionKeys = Object.keys(permissions);
    
    console.group('📋 All Current Permissions');
    if (permissionKeys.length === 0) {
      console.log('❌ No permissions loaded');
    } else {
      permissionKeys.forEach(permission => {
        console.log(`✅ ${permission}`);
      });
    }
    console.groupEnd();
  }
}
