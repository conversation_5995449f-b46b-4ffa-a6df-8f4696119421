import {Injectable, signal} from '@angular/core';
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {Store} from "@ngrx/store";
import {StorageService} from "../storage/storage.service";
import {Router} from "@angular/router";
import {NotificationService} from "../notification.service";
import {CustomErrorHandlerService} from "../custom-error-handler/custom-error-handler.service";
import {environment} from "../../../environments/environment";
import {map} from "rxjs/operators";
import {lastValueFrom} from "rxjs";
import {JwtHelperService} from "@auth0/angular-jwt";
import {NgxPermissionsService} from "ngx-permissions";

const headers = new HttpHeaders({
  'Content-Type': 'application/json',
  Accept: 'application/json',
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': '*'
});

export interface AuthTokenModel {
  token: string;
}

export interface Me {
  userId: string;
  username: string;
  fullName: string;
  email: string;
  phoneCode?: string;
  phone?: string;
  status: string;
  roles: Array<{
    uuid: string;
    name: string;
    displayName: string;
  }>;
  permissions: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  private tokenKey = 'currentClient';
  private permissionsKey = 'userPermissions';
  private userDataKey = 'userData';
  private jwtHelper = new JwtHelperService();
  // Initialized synchronously, then updated by async initializeAuthStatus
  authStatus = signal<boolean>(this.isSynchronouslyAuthenticated());

  constructor(
    private http: HttpClient,
    private storageService: StorageService,
    private customErrorHandler: CustomErrorHandlerService,
    private router: Router,
    private notificationService: NotificationService,
    private ngxPermissionsService: NgxPermissionsService
  ) {
    this.initializeAuthStatus(); // Asynchronously update authStatus
  }

   async login(formData: any): Promise<AuthTokenModel> {
    const body = {
      username: formData.username,
      password: formData.password
    }
    const loginObservable = this.http
      .post<AuthTokenModel>(environment.SERVER_URL + `/auth/login`, body, {headers});

    const tokenDetails = await lastValueFrom(loginObservable);

    if (tokenDetails && tokenDetails.token) {
      // Store in both localStorage (for immediate access) and Ionic storage (for persistence)
      localStorage.setItem(this.tokenKey, tokenDetails.token);
      await this.storageService.set(this.tokenKey, tokenDetails.token);
      this.authStatus.set(true);
    }

    return tokenDetails;
  }

  async logout() {
    // Clear all stored data
    await this.storageService.remove(this.tokenKey);
    await this.storageService.remove(this.permissionsKey);
    await this.storageService.remove(this.userDataKey);

    // Clear localStorage mirrors
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.permissionsKey);
    localStorage.removeItem(this.userDataKey);

    this.authStatus.set(false);
    this.ngxPermissionsService.flushPermissions();
    return this.router.navigate(['/login']);
  }

  async me(): Promise<Me> {
    const meObservable = this.http
      .get<Me>(environment.SERVER_URL + `/me`, {headers})
      .pipe(
        map(me => {
          console.log('found me', me);
          if (me.permissions) {
            this.ngxPermissionsService.loadPermissions(me.permissions);
            // Persist permissions and user data for future sessions
            this.persistPermissions(me.permissions);
            this.persistUserData(me);
          }
          return me;
        })
      );
    return await lastValueFrom(meObservable);
  }

  private isSynchronouslyAuthenticated(): boolean {
    if (!this.jwtHelper) {
        this.jwtHelper = new JwtHelperService();
    }
    const token = localStorage.getItem(this.tokenKey);
    return token ? !this.jwtHelper.isTokenExpired(token) : false;
  }

  isAuthenticated(): boolean {
    return this.authStatus();
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  async initializeAuthStatus(): Promise<void> {
    try {
        const token = await this.storageService.get(this.tokenKey);
        if (token && !this.jwtHelper.isTokenExpired(token)) {
            this.authStatus.set(true);
            localStorage.setItem(this.tokenKey, token); // Sync localStorage with StorageService

            // Restore permissions if available
            await this.restorePermissions();
        } else {
            this.authStatus.set(false);
            localStorage.removeItem(this.tokenKey); // Ensure localStorage is also cleared
            // Clear any stale permissions
            this.ngxPermissionsService.flushPermissions();
            await this.clearStoredPermissions();
        }
    } catch (error) {
        console.error("Error initializing auth status from storage", error);
        this.authStatus.set(false);
        localStorage.removeItem(this.tokenKey); // Clear localStorage on error too
        this.ngxPermissionsService.flushPermissions();
        await this.clearStoredPermissions();
    }
  }

  /**
   * Persist user permissions to storage
   */
  private async persistPermissions(permissions: string[]): Promise<void> {
    try {
      // Store in both localStorage (for immediate access) and Ionic storage (for persistence)
      localStorage.setItem(this.permissionsKey, JSON.stringify(permissions));
      await this.storageService.set(this.permissionsKey, permissions);
      console.log('Permissions persisted successfully', permissions);
    } catch (error) {
      console.error('Error persisting permissions:', error);
    }
  }

  /**
   * Persist user data to storage
   */
  private async persistUserData(userData: Me): Promise<void> {
    try {
      // Store in both localStorage (for immediate access) and Ionic storage (for persistence)
      localStorage.setItem(this.userDataKey, JSON.stringify(userData));
      await this.storageService.set(this.userDataKey, userData);
      console.log('User data persisted successfully');
    } catch (error) {
      console.error('Error persisting user data:', error);
    }
  }

  /**
   * Restore permissions from storage
   */
  private async restorePermissions(): Promise<boolean> {
    try {
      // Try to get from localStorage first (faster), then fallback to storage
      let permissions: string[] | null = null;

      const localStoragePermissions = localStorage.getItem(this.permissionsKey);
      if (localStoragePermissions) {
        permissions = JSON.parse(localStoragePermissions);
      } else {
        permissions = await this.storageService.get(this.permissionsKey);
        if (permissions) {
          // Sync to localStorage for faster future access
          localStorage.setItem(this.permissionsKey, JSON.stringify(permissions));
        }
      }

      if (permissions && Array.isArray(permissions) && permissions.length > 0) {
        // Validate that all permissions start with ROLE_ prefix
        const validPermissions = permissions.filter(p => p.startsWith('ROLE_'));
        if (validPermissions.length > 0) {
          this.ngxPermissionsService.loadPermissions(validPermissions);
          console.log('Permissions restored successfully', validPermissions);
          return true;
        }
      }

      console.log('No valid permissions found to restore');
      return false;
    } catch (error) {
      console.error('Error restoring permissions:', error);
      return false;
    }
  }

  /**
   * Clear stored permissions from all storage locations
   */
  private async clearStoredPermissions(): Promise<void> {
    try {
      await this.storageService.remove(this.permissionsKey);
      await this.storageService.remove(this.userDataKey);
      localStorage.removeItem(this.permissionsKey);
      localStorage.removeItem(this.userDataKey);
    } catch (error) {
      console.error('Error clearing stored permissions:', error);
    }
  }

  /**
   * Get stored user data
   */
  async getStoredUserData(): Promise<Me | null> {
    try {
      // Try localStorage first, then storage
      const localStorageData = localStorage.getItem(this.userDataKey);
      if (localStorageData) {
        return JSON.parse(localStorageData);
      }

      const userData = await this.storageService.get(this.userDataKey);
      if (userData) {
        // Sync to localStorage for faster future access
        localStorage.setItem(this.userDataKey, JSON.stringify(userData));
        return userData;
      }

      return null;
    } catch (error) {
      console.error('Error getting stored user data:', error);
      return null;
    }
  }

  /**
   * Check if permissions are currently loaded
   */
  hasPermissionsLoaded(): boolean {
    const permissions = this.ngxPermissionsService.getPermissions();
    return Object.keys(permissions).length > 0;
  }

  /**
   * Force refresh permissions from server
   */
  async refreshPermissions(): Promise<boolean> {
    try {
      if (this.isAuthenticated()) {
        const userData = await this.me();
        return userData.permissions && userData.permissions.length > 0;
      }
      return false;
    } catch (error) {
      console.error('Error refreshing permissions:', error);
      return false;
    }
  }
}
