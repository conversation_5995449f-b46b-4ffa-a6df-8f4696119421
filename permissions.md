# Permission System Documentation

## Overview
This document defines the complete permission system for the approval flow application, following the exact `ROLE_ENTITY_ACTION` pattern from the existing backend seeding method.

## Backend Permission Seeding

The following permissions should be added to your existing seeding method. All permissions follow the `ROLE_ENTITY_ACTION` pattern:

```java
@Override
public void seed() {
    log.info("Seeding permissions...");
    List<Permission> permissions = new ArrayList<Permission>() {
        {
            // dashboard
            add(new Permission("ROLE_DASHBOARD_VIEW", "Can View Dashboard", "DASHBOARD"));

            // roles
            add(new Permission("ROLE_ROLES_VIEW", "Can View Roles", "UAA"));
            add(new Permission("ROLE_ROLES_ADD", "Can Add New Role", "UAA"));
            add(new Permission("ROLE_ROLES_EDIT", "Can Edit Role", "UAA"));
            add(new Permission("ROLE_ROLES_DELETE", "Can Delete Role", "UAA"));

            // users
            add(new Permission("ROLE_USERS_VIEW", "Can View Users", "UAA"));
            add(new Permission("ROLE_USERS_ADD", "Can Add New User", "UAA"));
            add(new Permission("ROLE_USERS_EDIT", "Can Edit User", "UAA"));
            add(new Permission("ROLE_USERS_DELETE", "Can Delete User", "UAA"));
            add(new Permission("ROLE_USERS_DEACTIVATE", "Can Deactivate/Reactivate User", "UAA"));
            add(new Permission("ROLE_USERS_ASSIGN_ROLES", "Can Assign Roles to User", "UAA"));

            // requests
            add(new Permission("ROLE_REQUESTS_CREATE", "Can Create Approval Requests", "REQUESTS"));
            add(new Permission("ROLE_REQUESTS_VIEW", "Can View Approval Requests", "REQUESTS"));
            add(new Permission("ROLE_REQUESTS_VIEW_ALL", "Can View All Approval Requests", "REQUESTS"));
            add(new Permission("ROLE_REQUESTS_APPROVE", "Can Approve/Reject Requests", "REQUESTS"));
            add(new Permission("ROLE_REQUESTS_EDIT", "Can Edit Own Requests", "REQUESTS"));
            add(new Permission("ROLE_REQUESTS_DELETE", "Can Delete Own Requests", "REQUESTS"));

            // expenses
            add(new Permission("ROLE_EXPENSES_CREATE", "Can Create Expense Reports", "EXPENSES"));
            add(new Permission("ROLE_EXPENSES_VIEW", "Can View Own Expense Reports", "EXPENSES"));
            add(new Permission("ROLE_EXPENSES_VIEW_ALL", "Can View All Expense Reports", "EXPENSES"));
            add(new Permission("ROLE_EXPENSES_EDIT", "Can Edit Own Expense Reports", "EXPENSES"));
            add(new Permission("ROLE_EXPENSES_DELETE", "Can Delete Own Expense Reports", "EXPENSES"));
            add(new Permission("ROLE_EXPENSES_APPROVE", "Can Approve/Reject Expense Reports", "EXPENSES"));

            // profile
            add(new Permission("ROLE_PROFILE_VIEW", "Can View Own Profile", "PROFILE"));
            add(new Permission("ROLE_PROFILE_EDIT", "Can Edit Own Profile", "PROFILE"));

            // settings
            add(new Permission("ROLE_SETTINGS_VIEW", "Can View System Settings", "SETTINGS"));
            add(new Permission("ROLE_SETTINGS_EDIT", "Can Edit System Settings", "SETTINGS"));
        }
    };

    // Save permissions logic here...
}
```

## Permission Categories (Following ROLE_ENTITY_ACTION Pattern)

### 1. Dashboard Permissions
- `ROLE_DASHBOARD_VIEW` - Basic dashboard access

### 2. User Management (UAA Category)
- `ROLE_USERS_VIEW` - View user list and details
- `ROLE_USERS_ADD` - Create new users
- `ROLE_USERS_EDIT` - Edit user information
- `ROLE_USERS_DELETE` - Delete users
- `ROLE_USERS_DEACTIVATE` - Activate/deactivate users
- `ROLE_USERS_ASSIGN_ROLES` - Assign roles to users

### 3. Role Management (UAA Category)
- `ROLE_ROLES_VIEW` - View role list and details
- `ROLE_ROLES_ADD` - Create new roles
- `ROLE_ROLES_EDIT` - Edit role information
- `ROLE_ROLES_DELETE` - Delete roles

### 4. Request Management (REQUESTS Category)
- `ROLE_REQUESTS_CREATE` - Create approval requests
- `ROLE_REQUESTS_VIEW` - View own requests
- `ROLE_REQUESTS_VIEW_ALL` - View all requests in system
- `ROLE_REQUESTS_APPROVE` - Approve/reject requests
- `ROLE_REQUESTS_EDIT` - Edit own requests
- `ROLE_REQUESTS_DELETE` - Delete own requests

### 5. Expense Management (EXPENSES Category)
- `ROLE_EXPENSES_CREATE` - Create expense reports
- `ROLE_EXPENSES_VIEW` - View own expense reports
- `ROLE_EXPENSES_VIEW_ALL` - View all expense reports
- `ROLE_EXPENSES_EDIT` - Edit own expense reports
- `ROLE_EXPENSES_DELETE` - Delete own expense reports
- `ROLE_EXPENSES_APPROVE` - Approve/reject expense reports

### 6. Profile Management (PROFILE Category)
- `ROLE_PROFILE_VIEW` - View own profile
- `ROLE_PROFILE_EDIT` - Edit own profile

### 7. Settings Management (SETTINGS Category)
- `ROLE_SETTINGS_VIEW` - View system settings
- `ROLE_SETTINGS_EDIT` - Edit system settings

## Frontend Implementation

### Menu Item Permissions
```typescript
export const MENU_PERMISSIONS = {
  DASHBOARD: ['ROLE_DASHBOARD_VIEW'],
  REQUESTS: ['ROLE_REQUESTS_VIEW', 'ROLE_REQUESTS_CREATE'],
  APPROVALS: ['ROLE_REQUESTS_APPROVE', 'ROLE_REQUESTS_VIEW_ALL'],
  USERS: ['ROLE_USERS_VIEW', 'ROLE_USERS_ADD'],
  ROLES: ['ROLE_ROLES_VIEW', 'ROLE_ROLES_ADD'],
  EXPENSES: ['ROLE_EXPENSES_VIEW', 'ROLE_EXPENSES_CREATE'],
  PROFILE: ['ROLE_PROFILE_VIEW'],
  SETTINGS: ['ROLE_SETTINGS_VIEW', 'ROLE_SETTINGS_EDIT']
};
```

### Usage in Templates
```html
<!-- Menu items with permission guards -->
<ion-item *ngxPermissionsOnly="p.permissions" [routerLink]="p.url">

<!-- Component-level permission guards -->
<ion-button *ngxPermissionsOnly="['ROLE_USERS_ADD', 'ROLE_USERS_VIEW']">
  Add User
</ion-button>
```

## Role Suggestions

### Admin Role
- All user management: `ROLE_USERS_VIEW`, `ROLE_USERS_ADD`, `ROLE_USERS_EDIT`, `ROLE_USERS_DELETE`, `ROLE_USERS_DEACTIVATE`, `ROLE_USERS_ASSIGN_ROLES`
- All role management: `ROLE_ROLES_VIEW`, `ROLE_ROLES_ADD`, `ROLE_ROLES_EDIT`, `ROLE_ROLES_DELETE`
- Settings management: `ROLE_SETTINGS_VIEW`, `ROLE_SETTINGS_EDIT`
- Dashboard access: `ROLE_DASHBOARD_VIEW`

### Manager Role
- Request approval: `ROLE_REQUESTS_APPROVE`, `ROLE_REQUESTS_VIEW_ALL`
- Expense approval: `ROLE_EXPENSES_APPROVE`, `ROLE_EXPENSES_VIEW_ALL`
- Dashboard access: `ROLE_DASHBOARD_VIEW`

### User Role
- Request management: `ROLE_REQUESTS_CREATE`, `ROLE_REQUESTS_VIEW`, `ROLE_REQUESTS_EDIT`
- Expense management: `ROLE_EXPENSES_CREATE`, `ROLE_EXPENSES_VIEW`, `ROLE_EXPENSES_EDIT`
- Profile management: `ROLE_PROFILE_VIEW`, `ROLE_PROFILE_EDIT`
- Dashboard access: `ROLE_DASHBOARD_VIEW`

## Security Notes
- All permissions must be validated on both frontend and backend
- Frontend permissions are for UX only - backend validation is mandatory
- Use route guards for additional security layers
- Permissions are loaded from `/me` endpoint and stored in ngx-permissions service
