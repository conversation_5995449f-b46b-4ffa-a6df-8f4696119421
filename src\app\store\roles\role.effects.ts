import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, mergeMap, catchError, tap } from 'rxjs/operators';
import { Apollo } from 'apollo-angular';
import * as RoleActions from './role.actions';
import { LIST_ROLES, SAVE_ROLE, LIST_PERMISSIONS } from '../../services/graphql/role.graphql';
import { NotificationService } from '../../services/notification.service';

@Injectable()
export class RoleEffects {

  loadRoles$ = createEffect(() => this.actions$.pipe(
    ofType(RoleActions.loadRoles),
    mergeMap(() => this.apollo.watchQuery<any>({
      query: LIST_ROLES,
      fetchPolicy: 'network-only'
    }).valueChanges
      .pipe(
        map(result => {
          console.log('Load Roles Response:', result);
          if (result.data?.listRoles?.status && result.data.listRoles.dataList) {
            const roles = result.data.listRoles.dataList;
            return RoleActions.loadRolesSuccess({ roles });
          } else {
            const error = result.data?.listRoles?.errorDescription || 'Failed to load roles';
            return RoleActions.loadRolesFailure({ error });
          }
        }),
        catchError(error => {
          console.error('Load Roles Error:', error);
          return of(RoleActions.loadRolesFailure({ error }));
        })
      )
    )
  ));

  createRole$ = createEffect(() => this.actions$.pipe(
    ofType(RoleActions.createRole),
    mergeMap(({ roleDto }) => this.apollo.mutate<any>({
      mutation: SAVE_ROLE,
      variables: { roleDto },
      refetchQueries: [{ query: LIST_ROLES }]
    })
      .pipe(
        map(result => {
          console.log('Create Role Response:', result);
          if (result.data?.saveRole?.status && result.data.saveRole.data) {
            return RoleActions.createRoleSuccess({ role: result.data.saveRole.data });
          } else {
            const error = result.data?.saveRole?.errorDescription || 'Failed to create role';
            return RoleActions.createRoleFailure({ error });
          }
        }),
        catchError(error => {
          console.error('Create Role Error:', error);
          return of(RoleActions.createRoleFailure({ error }));
        })
      )
    )
  ));

  updateRole$ = createEffect(() => this.actions$.pipe(
    ofType(RoleActions.updateRole),
    mergeMap(({ roleDto }) => this.apollo.mutate<any>({
      mutation: SAVE_ROLE,
      variables: { roleDto },
      refetchQueries: [{ query: LIST_ROLES }]
    })
      .pipe(
        map(result => {
          console.log('Update Role Response:', result);
          if (result.data?.saveRole?.status && result.data.saveRole.data) {
            return RoleActions.updateRoleSuccess({ role: result.data.saveRole.data });
          } else {
            const error = result.data?.saveRole?.errorDescription || 'Failed to update role';
            return RoleActions.updateRoleFailure({ error });
          }
        }),
        catchError(error => {
          console.error('Update Role Error:', error);
          return of(RoleActions.updateRoleFailure({ error }));
        })
      )
    )
  ));

  // Success notifications
  createRoleSuccess$ = createEffect(() => this.actions$.pipe(
    ofType(RoleActions.createRoleSuccess),
    tap(({ role }) => {
      this.notificationService.showSuccess(`Role "${role.displayName}" created successfully!`);
    })
  ), { dispatch: false });

  updateRoleSuccess$ = createEffect(() => this.actions$.pipe(
    ofType(RoleActions.updateRoleSuccess),
    tap(({ role }) => {
      this.notificationService.showSuccess(`Role "${role.displayName}" updated successfully!`);
    })
  ), { dispatch: false });

  // Error notifications
  roleFailure$ = createEffect(() => this.actions$.pipe(
    ofType(
      RoleActions.loadRolesFailure,
      RoleActions.createRoleFailure,
      RoleActions.updateRoleFailure
    ),
    tap(({ error }) => {
      const errorMessage = typeof error === 'string' ? error : error?.message || 'An error occurred';
      this.notificationService.showError(errorMessage);
    })
  ), { dispatch: false });

  loadPermissions$ = createEffect(() => this.actions$.pipe(
    ofType(RoleActions.loadPermissions),
    mergeMap(() => this.apollo.watchQuery<any>({
      query: LIST_PERMISSIONS,
      fetchPolicy: 'network-only'
    }).valueChanges
      .pipe(
        map(result => {
          console.log('Load Permissions Response:', result);
          if (result.data?.listPermissions?.status && result.data.listPermissions.dataList) {
            const permissions = result.data.listPermissions.dataList;
            return RoleActions.loadPermissionsSuccess({ permissions });
          } else {
            const error = result.data?.listPermissions?.errorDescription || 'Failed to load permissions';
            return RoleActions.loadPermissionsFailure({ error });
          }
        }),
        catchError(error => {
          console.error('Load Permissions Error:', error);
          return of(RoleActions.loadPermissionsFailure({ error }));
        })
      )
    )
  ));

  constructor(
    private actions$: Actions,
    private apollo: Apollo,
    private notificationService: NotificationService
  ) {}
}
