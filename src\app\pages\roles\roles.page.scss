// Roles Page Styles
// Uses global list-pages styling - no custom styles needed

.permissions-section {
  .permission-group {
    border: 1px solid var(--ion-color-light);
    border-radius: 8px;
    padding: 16px;
    background: var(--ion-color-light-tint);

    h4 {
      margin: 0 0 12px 0;
      color: var(--ion-color-primary);
      font-weight: 600;
    }
  }

  .permission-item {
    --padding-start: 0;
    --padding-end: 0;
    --inner-padding-end: 0;
    margin-bottom: 8px;

    ion-checkbox {
      margin-right: 12px;
    }

    ion-label {
      h3 {
        margin: 0 0 4px 0;
        font-size: 14px;
        font-weight: 500;
      }

      p {
        margin: 0;
        font-size: 12px;
        color: var(--ion-color-medium);
      }
    }
  }
}