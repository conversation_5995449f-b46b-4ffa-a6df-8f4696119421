export interface Role {
  uuid: string;
  name: string;
  displayName: string;
  permissions?: Permission[];
  createdAt?: string;
  updatedAt?: string;
}

export interface Permission {
  uuid: string;
  name: string;
  description: string;
  groupName: string;
}

export interface RoleDto {
  uuid?: string;
  displayName: string;
  permissionUuids?: string[];
}

export interface RoleListItem extends Role {
  // Additional properties for list display if needed
  userCount?: number;
}

// GraphQL Response types
export interface GqlResponseDto<T> {
  status: boolean;
  code: string;
  data?: T;
  dataList?: T[];
  errorDescription?: string;
  extras?: any;
}
