import {Injectable} from '@angular/core';
import {Storage} from '@ionic/storage-angular';

@Injectable({
  providedIn: 'root'
})
export class StorageService {

  private _storage: Storage | null = null;

  constructor(private storage: Storage) {
    this.init();
  }

  async init() {
    // Create a new storage instance if one doesn't already exist.
    this._storage = await this.storage.create();
  }

  // Set a key/value pair
  public async set(key: string, value: any): Promise<any> {
    await this.ready();
    return this._storage?.set(key, value);
  }

  // Get a value by key
  public async get(key: string): Promise<any> {
    await this.ready();
    return this._storage?.get(key);
  }

  // Remove a key/value pair
  public async remove(key: string): Promise<any> {
    await this.ready();
    return this._storage?.remove(key);
  }

  // Clear all storage
  public async clear(): Promise<void> {
    await this.ready();
    return this._storage?.clear();
  }

  // Check if the storage is ready
  private async ready(): Promise<void> {
    if (!this._storage) {
      // Wait for the init() method to complete if it hasn't already.
      // This typically means waiting for the promise returned by this.storage.create() to resolve.
      // Adding a small delay or a more sophisticated ready check might be needed
      // if constructor and init are not guaranteed to complete before first use.
      // For this implementation, we assume `init` is called and awaited appropriately at app startup.
      // A more robust solution might involve a BehaviorSubject or a Promise in `init`
      // that `ready` can subscribe/await.
      // However, given Ionic's Storage typical usage, `create()` is often called at app bootstrap.
      await this.init(); // Re-attempt init if not ready, though this might need refinement
                          // depending on how `init` is guaranteed to be called first.
                          // A simple check like this might not be sufficient if `set/get/remove` are called immediately
                          // after service construction without `IonicStorageModule.forRoot()` having resolved.
    }
    // If _storage is still null after attempting init, throw an error.
    if (!this._storage) {
      throw new Error('Storage not initialized. Ensure StorageService.init() is called and awaited.');
    }
  }
}
