import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { NgxPermissionsService } from 'ngx-permissions';
import { AuthService } from '../services/auth/auth.service';
import { PermissionInitService } from '../services/permission-init.service';
import { PERMISSIONS } from '../constants/permissions';

export const managerGuard: CanActivateFn = async (_route, _state) => {
  const permissionsService = inject(NgxPermissionsService);
  const authService = inject(AuthService);
  const router = inject(Router);
  const permissionInitService = inject(PermissionInitService);

  try {
    // Ensure permissions are initialized
    await permissionInitService.initializePermissions();

    // Ensure user is authenticated first
    if (!authService.isAuthenticated()) {
      router.navigate(['/login']);
      return false;
    }

    // Ensure permissions are loaded
    if (!authService.hasPermissionsLoaded()) {
      const permissionsLoaded = await authService.refreshPermissions();
      if (!permissionsLoaded) {
        router.navigate(['/login']);
        return false;
      }
    }

    // Check if user has manager or admin permissions
    const permissions = permissionsService.getPermissions();
    const hasManagerPermission =
      permissions[PERMISSIONS.ROLE_USERS_VIEW] ||
      permissions[PERMISSIONS.ROLE_REQUESTS_APPROVE] ||
      permissions[PERMISSIONS.ROLE_REQUESTS_VIEW_ALL];

    if (hasManagerPermission) {
      return true;
    } else {
      // Redirect to dashboard if not manager/admin
      router.navigate(['/menu/dashboard']);
      return false;
    }
  } catch (error) {
    console.error('Error in manager guard:', error);
    router.navigate(['/login']);
    return false;
  }
};
