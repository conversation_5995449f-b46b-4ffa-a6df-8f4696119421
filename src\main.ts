import { enableProdMode } from '@angular/core'; // Added importProvidersFrom and enableProdMode
import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { environment } from './environments/environment';
import {appConfig} from "./app/app.config";

if (environment.production) { // Added from example
  enableProdMode();
}

bootstrapApplication(AppComponent, appConfig).catch(err => console.error(err));
