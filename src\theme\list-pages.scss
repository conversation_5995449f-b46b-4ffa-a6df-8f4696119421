// Global List Pages Styling
// =============================================================================
// Reusable styles for all list pages (users, requests, approvals, etc.)

// List Page Layout
// -----------------------------------------------------------------------------
.list-page {
  // Page Header
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--app-spacing-2xl); // Increased spacing
    gap: var(--app-spacing-lg);

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      margin-bottom: var(--app-spacing-xl);
    }

    .header-content {
      flex: 1;

      .page-title {
        font-size: var(--app-font-size-3xl);
        font-weight: var(--app-font-weight-bold);
        color: var(--app-white);
        margin: 0 0 var(--app-spacing-sm) 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

        @media (max-width: 768px) {
          font-size: var(--app-font-size-2xl);
        }
      }

      .page-subtitle {
        font-size: var(--app-font-size-lg);
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

        @media (max-width: 768px) {
          font-size: var(--app-font-size-base);
        }
      }
    }

    .header-actions {
      flex-shrink: 0;

      @media (max-width: 768px) {
        align-self: stretch;
      }
    }
  }

  // Card Spacing
  .list-cards {
    display: flex;
    flex-direction: column;
    gap: var(--app-spacing-xl); // Increased gap between cards

    @media (max-width: 768px) {
      gap: var(--app-spacing-lg);
    }

    .app-card {
      &:first-child {
        // Search/Filters card
        margin-bottom: 0;
      }

      &:last-child {
        // List content card
        margin-bottom: 0;
      }
    }
  }

  // Search and Filters Card
  .search-filters-card {
    .search-filters {
      .search-container {
        margin-bottom: var(--app-spacing-md);

        .app-searchbar {
          --background: var(--app-white);
          --border-radius: var(--app-radius-lg);
          --box-shadow: var(--app-shadow-sm);
          --color: var(--app-gray-800);
          --placeholder-color: var(--app-gray-500);
          --icon-color: var(--app-gray-500);
        }
      }

      .filters-container {
        display: flex;
        flex-direction: column;
        gap: var(--app-spacing-md);

        @media (min-width: 768px) {
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .filter-chips {
          display: flex;
          flex-wrap: wrap;
          gap: var(--app-spacing-sm);

          .filter-chip {
            --background: rgba(255, 255, 255, 0.1);
            --color: var(--app-white);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all var(--app-transition-normal);
            cursor: pointer;

            &:hover {
              --background: rgba(255, 255, 255, 0.2);
            }

            &.selected {
              --background: var(--app-primary);
              --color: var(--app-white);
              border-color: var(--app-primary);
            }
          }
        }

        .filter-toggles {
          display: flex;
          gap: var(--app-spacing-sm);

          .filter-chip {
            --background: rgba(255, 255, 255, 0.1);
            --color: var(--app-white);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all var(--app-transition-normal);
            cursor: pointer;

            &:hover {
              --background: rgba(255, 255, 255, 0.2);
            }

            &.selected {
              --background: var(--app-success);
              --color: var(--app-white);
              border-color: var(--app-success);
            }

            ion-icon {
              margin-right: var(--app-spacing-xs);
            }
          }
        }
      }
    }
  }

  // List Content Card
  .list-content-card {
    min-height: 400px; // Ensure minimum height for better visual balance

    // Loading States
    .loading-container {
      padding: var(--app-spacing-2xl);

      .skeleton-list {
        display: flex;
        flex-direction: column;
        gap: var(--app-spacing-lg);

        .skeleton-item {
          display: flex;
          align-items: center;
          gap: var(--app-spacing-md);
          padding: var(--app-spacing-lg);

          .skeleton-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: var(--app-spacing-sm);
          }
        }
      }
    }

    // Empty State
    .empty-state {
      text-align: center;
      padding: var(--app-spacing-3xl) var(--app-spacing-lg);
      color: var(--app-gray-500);

      .empty-icon {
        font-size: 4rem;
        margin-bottom: var(--app-spacing-lg);
        opacity: 0.5;
      }

      h3 {
        font-size: var(--app-font-size-xl);
        font-weight: var(--app-font-weight-semibold);
        margin: 0 0 var(--app-spacing-sm) 0;
      }

      p {
        font-size: var(--app-font-size-base);
        margin: 0;
        opacity: 0.8;
      }
    }

    // List Items
    .list-items {
      background: transparent;

      .list-item {
        --background: transparent;
        --border-color: rgba(0, 0, 0, 0.1);
        --padding-start: var(--app-spacing-lg);
        --padding-end: var(--app-spacing-lg);
        --min-height: 80px;
        margin-bottom: var(--app-spacing-md); // Increased spacing between items
        border-radius: var(--app-radius-lg);
        transition: all var(--app-transition-normal);

        &:hover {
          --background: rgba(255, 255, 255, 0.05);
          transform: translateY(-1px);
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    // Infinite Scroll
    .infinite-scroll {
      margin-top: var(--app-spacing-xl);

      .infinite-scroll-content {
        .loading-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: var(--app-spacing-lg);
          gap: var(--app-spacing-sm);
          background: rgba(255, 255, 255, 0.1);
          border-radius: var(--app-radius-lg);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          margin: 0 var(--app-spacing-md);

          .loading-spinner {
            --color: var(--app-white);
            width: 32px;
            height: 32px;
          }

          .loading-text {
            font-size: var(--app-font-size-sm);
            color: var(--app-white);
            margin: 0;
            text-align: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          }

          @media (max-width: 768px) {
            padding: var(--app-spacing-md);
            margin: 0 var(--app-spacing-sm);
          }
        }
      }
    }

    // End of List
    .end-of-list {
      margin-top: var(--app-spacing-xl);
      padding: var(--app-spacing-xl) var(--app-spacing-lg);

      .end-message {
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--app-radius-lg);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: var(--app-spacing-xl);
        margin: 0 var(--app-spacing-md);

        .end-icon {
          font-size: 2.5rem;
          color: var(--app-white);
          margin-bottom: var(--app-spacing-md);
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        p {
          font-size: var(--app-font-size-base);
          font-weight: var(--app-font-weight-medium);
          margin: 0 0 var(--app-spacing-xs) 0;
          color: var(--app-white);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        small {
          font-size: var(--app-font-size-sm);
          color: rgba(255, 255, 255, 0.8);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 768px) {
          padding: var(--app-spacing-lg);
          margin: 0;
        }
      }

      @media (max-width: 768px) {
        padding: var(--app-spacing-lg) var(--app-spacing-sm);
      }
    }
  }
}
