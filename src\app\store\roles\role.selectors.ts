import { createFeatureSelector, createSelector } from '@ngrx/store';
import { RoleState, selectAll, selectEntities } from './role.reducer';
import { AppState } from '../index';

export const selectRoleState = createFeatureSelector<AppState, RoleState>('roles');

export const selectAllRoles = createSelector(
  selectRoleState,
  selectAll
);

export const selectRoleEntities = createSelector(
  selectRoleState,
  selectEntities
);

export const selectRoleLoading = createSelector(
  selectRoleState,
  (state: RoleState) => state.loading
);

export const selectRoleError = createSelector(
  selectRoleState,
  (state: RoleState) => state.error
);

export const selectSelectedRoleId = createSelector(
  selectRoleState,
  (state: RoleState) => state.selectedRoleId
);

export const selectSelectedRole = createSelector(
  selectRoleEntities,
  selectSelectedRoleId,
  (entities, selectedId) => selectedId ? entities[selectedId] : null
);

export const selectRoleById = (roleId: string) => createSelector(
  selectRoleEntities,
  (entities) => entities[roleId]
);

export const selectRolesCount = createSelector(
  selectRoleState,
  (state: RoleState) => state.ids.length
);
