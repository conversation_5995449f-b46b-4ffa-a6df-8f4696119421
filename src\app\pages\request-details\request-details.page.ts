import { Component, OnInit, OnD<PERSON>roy, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import {
  IonContent,
  IonButton,
  IonIcon,
  IonSpinner
} from '@ionic/angular/standalone';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { addIcons } from 'ionicons';
import {
  arrowBackOutline,
  alertCircleOutline,
  refreshOutline,
  ellipsisHorizontalOutline,
  personOutline,
  checkmarkCircleOutline,
  checkmarkOutline,
  closeOutline,
  createOutline
} from 'ionicons/icons';

import { Request } from '../../models/request.model';
import { AppState } from '../../store';
import * as RequestActions from '../../store/requests/request.actions';
import * as RequestSelectors from '../../store/requests/request.selectors';

@Component({
  selector: 'app-request-details',
  templateUrl: './request-details.page.html',
  styleUrls: ['./request-details.page.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [IonContent, IonButton, IonIcon, IonSpinner, CommonModule, FormsModule]
})
export class RequestDetailsPage implements OnInit, OnDestroy {
  request: Request | null = null;
  loading$!: Observable<boolean>;
  error$!: Observable<any>;

  private destroy$ = new Subject<void>();
  private requestId: string = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private store: Store<AppState>
  ) {
    // Add icons
    addIcons({
      'arrow-back-outline': arrowBackOutline,
      'alert-circle-outline': alertCircleOutline,
      'refresh-outline': refreshOutline,
      'ellipsis-horizontal-outline': ellipsisHorizontalOutline,
      'person-outline': personOutline,
      'checkmark-circle-outline': checkmarkCircleOutline,
      'checkmark-outline': checkmarkOutline,
      'close-outline': closeOutline,
      'create-outline': createOutline
    });
  }

  ngOnInit() {
    // Get request ID from route params
    this.requestId = this.route.snapshot.paramMap.get('id') || '';

    // Initialize store selectors
    this.loading$ = this.store.select(RequestSelectors.selectRequestLoading);
    this.error$ = this.store.select(RequestSelectors.selectRequestError);

    // Load the specific request
    this.loadRequest();

    // Subscribe to the selected request
    this.store.select(RequestSelectors.selectRequestById(this.requestId))
      .pipe(takeUntil(this.destroy$))
      .subscribe(request => {
        this.request = request || null;
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadRequest() {
    if (this.requestId) {
      this.store.dispatch(RequestActions.loadRequestById({ requestId: this.requestId }));
    }
  }

  goBack() {
    this.location.back();
  }

  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'app-badge-warning';
      case 'approved':
        return 'app-badge-success';
      case 'rejected':
        return 'app-badge-danger';
      case 'completed':
        return 'app-badge-info';
      default:
        return 'app-badge-gray';
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  approveRequest() {
    if (this.request) {
      // TODO: Implement approval logic
      console.log('Approve request:', this.request.uuid);
    }
  }

  rejectRequest() {
    if (this.request) {
      // TODO: Implement rejection logic
      console.log('Reject request:', this.request.uuid);
    }
  }

  editRequest() {
    if (this.request) {
      // TODO: Navigate to edit request page
      console.log('Edit request:', this.request.uuid);
    }
  }
}
