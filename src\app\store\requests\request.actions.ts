import {createAction, props} from '@ngrx/store';
import {Request, RequestCreateDto} from '../../models/request.model';

// Load Requests
export const loadRequests = createAction(
  '[Request] Load Requests'
);

export const loadRequestsSuccess = createAction(
  '[Request] Load Requests Success',
  props<{ requests: Request[] }>()
);

export const loadRequestsFailure = createAction(
  '[Request] Load Requests Failure',
  props<{ error: any }>()
);

// Load Request by ID
export const loadRequestById = createAction(
  '[Request] Load Request By ID',
  props<{ requestId: string }>()
);

export const loadRequestByIdSuccess = createAction(
  '[Request] Load Request By ID Success',
  props<{ request: Request }>()
);

export const loadRequestByIdFailure = createAction(
  '[Request] Load Request By ID Failure',
  props<{ error: any }>()
);

// Create Request
export const createRequest = createAction(
  '[Request] Create Request',
  props<{ request: Omit<Request, 'uuid'> }>()
);

export const createRequestSuccess = createAction(
  '[Request] Create Request Success',
  props<{ request: Request }>()
);

export const createRequestFailure = createAction(
  '[Request] Create Request Failure',
  props<{ error: any }>()
);

// Update Request
export const updateRequest = createAction(
  '[Request] Update Request',
  props<{ request: Request }>()
);

export const updateRequestSuccess = createAction(
  '[Request] Update Request Success',
  props<{ request: Request }>()
);

export const updateRequestFailure = createAction(
  '[Request] Update Request Failure',
  props<{ error: any }>()
);

// Approve Request
export const approveRequest = createAction(
  '[Request] Approve Request',
  props<{ requestId: string, approverName: string }>()
);

export const approveRequestSuccess = createAction(
  '[Request] Approve Request Success',
  props<{ request: Request }>()
);

export const approveRequestFailure = createAction(
  '[Request] Approve Request Failure',
  props<{ error: any }>()
);

// Reject Request
export const rejectRequest = createAction(
  '[Request] Reject Request',
  props<{ requestId: string, approverName: string, remarks: string }>()
);

export const rejectRequestSuccess = createAction(
  '[Request] Reject Request Success',
  props<{ request: Request }>()
);

export const rejectRequestFailure = createAction(
  '[Request] Reject Request Failure',
  props<{ error: any }>()
);

// Complete Request
export const completeRequest = createAction(
  '[Request] Complete Request',
  props<{ requestId: string }>()
);

export const completeRequestSuccess = createAction(
  '[Request] Complete Request Success',
  props<{ request: Request }>()
);

export const completeRequestFailure = createAction(
  '[Request] Complete Request Failure',
  props<{ error: any }>()
);

// Save Request (using saveRequest mutation)
export const saveRequest = createAction(
  '[Request] Save Request',
  props<{ requestDto: RequestCreateDto }>()
);

export const saveRequestSuccess = createAction(
  '[Request] Save Request Success',
  props<{ request: Request }>()
);

export const saveRequestFailure = createAction(
  '[Request] Save Request Failure',
  props<{ error: any }>()
);
