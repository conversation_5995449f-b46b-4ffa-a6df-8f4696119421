import { Injectable } from '@angular/core';
import { NgxPermissionsService } from 'ngx-permissions';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { PERMISSIONS, PERMISSION_GROUPS, hasAnyPermission, hasAllPermissions } from '../constants/permissions';

@Injectable({
  providedIn: 'root'
})
export class PermissionService {

  constructor(private ngxPermissionsService: NgxPermissionsService) {}

  /**
   * Check if user has any of the specified permissions
   */
  hasAnyPermission(permissions: string[]): boolean {
    const userPermissions = this.getUserPermissions();
    return hasAnyPermission(userPermissions, permissions);
  }

  /**
   * Check if user has all of the specified permissions
   */
  hasAllPermissions(permissions: string[]): boolean {
    const userPermissions = this.getUserPermissions();
    return hasAllPermissions(userPermissions, permissions);
  }

  /**
   * Check if user has a specific permission
   */
  hasPermission(permission: string): boolean {
    const userPermissions = this.getUserPermissions();
    return userPermissions.includes(permission);
  }

  /**
   * Get all user permissions as array
   */
  getUserPermissions(): string[] {
    const permissions = this.ngxPermissionsService.getPermissions();
    return Object.keys(permissions);
  }

  /**
   * Check if user is admin
   */
  isAdmin(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.ROLE_USERS_VIEW,
      PERMISSIONS.ROLE_USERS_ADD,
      PERMISSIONS.ROLE_ROLES_VIEW,
      PERMISSIONS.ROLE_ROLES_ADD
    ]);
  }

  /**
   * Check if user is manager (has approval/management permissions)
   */
  isManager(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.ROLE_REQUESTS_APPROVE,
      PERMISSIONS.ROLE_REQUESTS_VIEW_ALL,
      PERMISSIONS.ROLE_EXPENSES_APPROVE
    ]);
  }

  /**
   * Check if user can manage users
   */
  canManageUsers(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.ROLE_USERS_VIEW,
      PERMISSIONS.ROLE_USERS_ADD,
      PERMISSIONS.ROLE_USERS_EDIT,
      PERMISSIONS.ROLE_USERS_DELETE
    ]);
  }

  /**
   * Check if user can manage roles
   */
  canManageRoles(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.ROLE_ROLES_VIEW,
      PERMISSIONS.ROLE_ROLES_ADD,
      PERMISSIONS.ROLE_ROLES_EDIT,
      PERMISSIONS.ROLE_ROLES_DELETE
    ]);
  }

  /**
   * Check if user can approve requests
   */
  canApproveRequests(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.ROLE_REQUESTS_APPROVE
    ]);
  }

  /**
   * Check if user can view all requests (not just their own)
   */
  canViewAllRequests(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.ROLE_REQUESTS_VIEW_ALL
    ]);
  }

  /**
   * Check if user can manage settings
   */
  canManageSettings(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.ROLE_SETTINGS_VIEW,
      PERMISSIONS.ROLE_SETTINGS_EDIT
    ]);
  }

  /**
   * Observable that emits when permissions change
   */
  getPermissions$(): Observable<string[]> {
    return this.ngxPermissionsService.permissions$.pipe(
      map(permissions => Object.keys(permissions))
    );
  }

  /**
   * Load permissions for the current user
   */
  loadPermissions(permissions: string[]): void {
    this.ngxPermissionsService.loadPermissions(permissions);
  }

  /**
   * Clear all permissions
   */
  clearPermissions(): void {
    this.ngxPermissionsService.flushPermissions();
  }

  /**
   * Get permission display name for UI
   */
  getPermissionDisplayName(permission: string): string {
    const displayNames: { [key: string]: string } = {
      [PERMISSIONS.ROLE_DASHBOARD_VIEW]: 'Dashboard Access',
      [PERMISSIONS.ROLE_USERS_VIEW]: 'View Users',
      [PERMISSIONS.ROLE_USERS_ADD]: 'Add Users',
      [PERMISSIONS.ROLE_USERS_EDIT]: 'Edit Users',
      [PERMISSIONS.ROLE_USERS_DELETE]: 'Delete Users',
      [PERMISSIONS.ROLE_USERS_DEACTIVATE]: 'Deactivate Users',
      [PERMISSIONS.ROLE_USERS_ASSIGN_ROLES]: 'Assign User Roles',
      [PERMISSIONS.ROLE_ROLES_VIEW]: 'View Roles',
      [PERMISSIONS.ROLE_ROLES_ADD]: 'Add Roles',
      [PERMISSIONS.ROLE_ROLES_EDIT]: 'Edit Roles',
      [PERMISSIONS.ROLE_ROLES_DELETE]: 'Delete Roles',
      [PERMISSIONS.ROLE_REQUESTS_CREATE]: 'Create Requests',
      [PERMISSIONS.ROLE_REQUESTS_VIEW]: 'View Own Requests',
      [PERMISSIONS.ROLE_REQUESTS_VIEW_ALL]: 'View All Requests',
      [PERMISSIONS.ROLE_REQUESTS_APPROVE]: 'Approve Requests',
      [PERMISSIONS.ROLE_REQUESTS_EDIT]: 'Edit Requests',
      [PERMISSIONS.ROLE_REQUESTS_DELETE]: 'Delete Requests',
      [PERMISSIONS.ROLE_EXPENSES_CREATE]: 'Create Expenses',
      [PERMISSIONS.ROLE_EXPENSES_VIEW]: 'View Own Expenses',
      [PERMISSIONS.ROLE_EXPENSES_VIEW_ALL]: 'View All Expenses',
      [PERMISSIONS.ROLE_EXPENSES_EDIT]: 'Edit Expenses',
      [PERMISSIONS.ROLE_EXPENSES_DELETE]: 'Delete Expenses',
      [PERMISSIONS.ROLE_EXPENSES_APPROVE]: 'Approve Expenses',
      [PERMISSIONS.ROLE_PROFILE_VIEW]: 'View Profile',
      [PERMISSIONS.ROLE_PROFILE_EDIT]: 'Edit Own Profile',
      [PERMISSIONS.ROLE_SETTINGS_VIEW]: 'View Settings',
      [PERMISSIONS.ROLE_SETTINGS_EDIT]: 'Edit Settings'
    };

    return displayNames[permission] || permission;
  }
}
