import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { ProfilePage } from './profile.page';
import { AuthService } from '../../services/auth/auth.service';

describe('ProfilePage', () => {
  let component: ProfilePage;
  let fixture: ComponentFixture<ProfilePage>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockLocation: jasmine.SpyObj<Location>;

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['logout']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const locationSpy = jasmine.createSpyObj('Location', ['back']);

    await TestBed.configureTestingModule({
      imports: [ProfilePage],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: Location, useValue: locationSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ProfilePage);
    component = fixture.componentInstance;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockLocation = TestBed.inject(Location) as jasmine.SpyObj<Location>;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default user data', () => {
    expect(component.user.name).toBe('John Doe');
    expect(component.user.email).toBe('<EMAIL>');
    expect(component.user.role).toBe('Technician');
    expect(component.user.department).toBe('Engineering');
  });

  it('should return correct role badge class', () => {
    expect(component.getRoleBadgeClass('admin')).toBe('role-admin');
    expect(component.getRoleBadgeClass('manager')).toBe('role-manager');
    expect(component.getRoleBadgeClass('technician')).toBe('role-technician');
    expect(component.getRoleBadgeClass('unknown')).toBe('role-default');
  });

  it('should call location.back() when goBack is called', () => {
    component.goBack();
    expect(mockLocation.back).toHaveBeenCalled();
  });

  it('should navigate to settings when openSettings is called', () => {
    component.openSettings();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/menu/settings']);
  });

  it('should call authService.logout when logout is called', () => {
    component.logout();
    expect(mockAuthService.logout).toHaveBeenCalled();
  });

  it('should log messages for placeholder methods', () => {
    spyOn(console, 'log');

    component.editAvatar();
    expect(console.log).toHaveBeenCalledWith('Edit avatar clicked');

    component.editField('email');
    expect(console.log).toHaveBeenCalledWith('Edit field clicked:', 'email');

    component.editProfile();
    expect(console.log).toHaveBeenCalledWith('Edit profile clicked');

    component.viewActivity();
    expect(console.log).toHaveBeenCalledWith('View activity clicked');

    component.viewNotifications();
    expect(console.log).toHaveBeenCalledWith('View notifications clicked');

    component.viewSecurity();
    expect(console.log).toHaveBeenCalledWith('View security clicked');
  });
});
