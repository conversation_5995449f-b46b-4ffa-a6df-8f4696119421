// User Status Enum
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED'
}

// Role interface for nested role objects
export interface UserRole {
  uuid: string;
  name: string;
  displayName: string;
}

// Main User interface aligned with API
export interface User {
  uuid: string;
  username: string;
  fullName: string;
  email: string;
  phoneCode?: string;
  phone?: string;
  status: UserStatus;
  roles: UserRole[];
  profileImage?: string;
  lastLogin?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface UserListItem extends User {
  // Additional properties for list display
  requestCount?: number;
}

// DTOs for API requests matching UserCreateRequestDto
export interface UserCreateRequestDto {
  uuid?: string; // Optional - if present, this is an update operation
  username: string;
  password?: string; // Required only for create operations
  fullName: string;
  email: string;
  phoneCode?: string;
  phone?: string;
  status?: UserStatus;
  roleUuids?: string[];
}

export interface UserFilterDto {
  search?: string;
  status?: UserStatus;
  roleUuid?: string;
  page?: number;
  size?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

export interface AssignRolesDto {
  userUuid: string;
  roleUuids: string[];
}

// API Response interfaces
export interface UserResponseDto {
  uuid: string;
  username: string;
  fullName: string;
  email: string;
  phoneCode?: string;
  phone?: string;
  status: UserStatus;
  roles: UserRole[];
  lastLogin?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface UserListResponseDto {
  users: UserResponseDto[];
  totalCount: number;
  page: number;
  size: number;
  totalPages: number;
}

export interface GqlUserResponse {
  status: string;
  code: string;
  data?: UserResponseDto;
  dataList?: UserResponseDto[];
  errorDescription?: string;
  extras?: any;
}

export const DEPARTMENTS = [
  'Engineering',
  'IT',
  'Operations',
  'Security',
  'Maintenance',
  'Administration'
] as const;
