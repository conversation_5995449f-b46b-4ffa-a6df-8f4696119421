<ion-header [translucent]="true" class="ion-no-border">
  <ion-toolbar>
    <ion-title>Sign Up</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <div class="signup-container">
    <ion-card>
      <ion-card-header>
        <ion-card-title class="ion-text-center">
          <ion-icon name="person-add" class="auth-icon"></ion-icon>
          Create Account
        </ion-card-title>
      </ion-card-header>

      <ion-card-content>
        <form [formGroup]="signupForm" (ngSubmit)="onSubmit()" novalidate>
          <ion-item class="ion-margin-bottom">
            <ion-icon name="person" slot="start"></ion-icon>
            <ion-label position="floating">Full Name</ion-label>
            <ion-input type="text" formControlName="name" autocomplete="name"></ion-input>
          </ion-item>
          <div *ngIf="isSubmitted && errorControl?.['name'].errors" class="error-message">
            <ion-text color="danger" *ngIf="errorControl?.['name'].errors?.['required']">
              Name is required.
            </ion-text>
            <ion-text color="danger" *ngIf="errorControl?.['name'].errors?.['minlength']">
              Name must be at least 2 characters long.
            </ion-text>
          </div>

          <ion-item class="ion-margin-bottom">
            <ion-icon name="mail" slot="start"></ion-icon>
            <ion-label position="floating">Email</ion-label>
            <ion-input type="email" formControlName="email" autocomplete="email"></ion-input>
          </ion-item>
          <div *ngIf="isSubmitted && errorControl?.['email'].errors" class="error-message">
            <ion-text color="danger" *ngIf="errorControl?.['email'].errors?.['required']">
              Email is required.
            </ion-text>
            <ion-text color="danger" *ngIf="errorControl?.['email'].errors?.['email']">
              Please enter a valid email address.
            </ion-text>
          </div>

          <ion-item class="ion-margin-bottom">
            <ion-icon name="lock-closed" slot="start"></ion-icon>
            <ion-label position="floating">Password</ion-label>
            <ion-input type="password" formControlName="password" autocomplete="new-password"></ion-input>
          </ion-item>
          <div *ngIf="isSubmitted && errorControl?.['password'].errors" class="error-message">
            <ion-text color="danger" *ngIf="errorControl?.['password'].errors?.['required']">
              Password is required.
            </ion-text>
            <ion-text color="danger" *ngIf="errorControl?.['password'].errors?.['minlength']">
              Password must be at least 6 characters long.
            </ion-text>
          </div>

          <ion-item class="ion-margin-bottom">
            <ion-icon name="lock-closed" slot="start"></ion-icon>
            <ion-label position="floating">Confirm Password</ion-label>
            <ion-input type="password" formControlName="confirmPassword" autocomplete="new-password"></ion-input>
          </ion-item>
          <div *ngIf="isSubmitted && errorControl?.['confirmPassword'].errors" class="error-message">
            <ion-text color="danger" *ngIf="errorControl?.['confirmPassword'].errors?.['required']">
              Please confirm your password.
            </ion-text>
          </div>
          <div *ngIf="isSubmitted && !passwordMatching && !errorControl?.['confirmPassword'].errors" class="error-message">
            <ion-text color="danger">
              Passwords do not match.
            </ion-text>
          </div>

          <ion-button expand="block" type="submit" class="ion-margin-top signup-button">
            <ion-icon name="person-add" slot="start"></ion-icon>
            Sign Up
          </ion-button>
        </form>

        <div class="login-link ion-text-center ion-margin-top">
          <p>Already have an account? <a [routerLink]="['/login']">Login</a></p>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
