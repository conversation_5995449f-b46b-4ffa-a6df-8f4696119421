import { Component, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  IonContent, IonHeader, IonTitle, IonToolbar, IonButtons,
  IonItem, IonLabel, IonList,
  IonButton, IonIcon, IonSearchbar, IonModal,
  IonInput, IonRefresher, IonRefresherContent,
  IonBadge, IonSkeletonText
} from '@ionic/angular/standalone';
import { NgxPermissionsModule } from 'ngx-permissions';
import { addIcons } from 'ionicons';
import {
  add, search, refresh, close, save, create, pencil,
  checkmarkCircle, alertCircle, business, businessOutline,
  addOutline, saveOutline, closeOutline, createOutline,
  searchOutline, calendarOutline
} from 'ionicons/icons';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { AppState } from '../../store';
import { Role, RoleDto } from '../../models/role.model';
import * as RoleActions from '../../store/roles/role.actions';
import * as RoleSelectors from '../../store/roles/role.selectors';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.page.html',
  styleUrls: ['./roles.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPermissionsModule,
    IonContent, IonHeader, IonTitle, IonToolbar, IonButtons,
    IonItem, IonLabel, IonList,
    IonButton, IonIcon, IonSearchbar, IonModal,
    IonInput, IonRefresher, IonRefresherContent,
    IonBadge, IonSkeletonText
  ]
})
export class RolesPage implements OnInit, OnDestroy {
  roles: Role[] = [];
  filteredRoles: Role[] = [];
  searchTerm = '';
  loading = false;

  // Modal state
  isModalOpen = false;
  modalMode: 'create' | 'edit' = 'create';
  selectedRole: Role | null = null;
  roleForm: FormGroup;

  // NgRx Observables
  roles$: Observable<Role[]>;
  loading$: Observable<boolean>;
  error$: Observable<any>;

  private destroy$ = new Subject<void>();

  constructor(
    private store: Store<AppState>,
    private formBuilder: FormBuilder
  ) {
    // Add icons
    addIcons({
      add, search, refresh, close, save, create, pencil,
      checkmarkCircle, alertCircle, business, businessOutline,
      addOutline, saveOutline, closeOutline, createOutline,
      searchOutline, calendarOutline
    });

    // Initialize NgRx observables
    this.roles$ = this.store.select(RoleSelectors.selectAllRoles);
    this.loading$ = this.store.select(RoleSelectors.selectRoleLoading);
    this.error$ = this.store.select(RoleSelectors.selectRoleError);

    // Initialize form
    this.roleForm = this.createRoleForm();
  }

  private createRoleForm(): FormGroup {
    return this.formBuilder.group({
      displayName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]]
    });
  }

  ngOnInit() {
    // Load roles from the store
    this.store.dispatch(RoleActions.loadRoles());

    // Subscribe to roles and update filtered roles
    this.roles$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(roles => {
      this.roles = roles;
      this.applyFilters();
    });

    // Subscribe to loading state
    this.loading$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(loading => {
      this.loading = loading;
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Method to apply search filter
  applyFilters() {
    this.filteredRoles = this.roles.filter(role => {
      if (this.searchTerm && this.searchTerm.trim() !== '') {
        const term = this.searchTerm.toLowerCase();
        return (
          role.displayName.toLowerCase().includes(term) ||
          role.name.toLowerCase().includes(term)
        );
      }
      return true;
    });
  }

  // Method to handle search input
  onSearchChange(event: any) {
    this.searchTerm = event.detail.value;
    this.applyFilters();
  }

  // Clear search
  clearSearch() {
    this.searchTerm = '';
    this.applyFilters();
  }

  // Refresh roles
  onRefresh(event: any) {
    this.store.dispatch(RoleActions.loadRoles());
    setTimeout(() => {
      event.target.complete();
    }, 1000);
  }

  // Open create role modal
  openCreateModal() {
    this.modalMode = 'create';
    this.selectedRole = null;
    this.roleForm.reset();
    this.isModalOpen = true;
  }

  // Open edit role modal
  openEditModal(role: Role) {
    this.modalMode = 'edit';
    this.selectedRole = role;
    this.roleForm.patchValue({
      displayName: role.displayName
    });
    this.isModalOpen = true;
  }

  // Close modal
  closeModal() {
    this.isModalOpen = false;
    this.selectedRole = null;
    this.roleForm.reset();
    this.store.dispatch(RoleActions.clearRoleError());
  }

  // Save role (create or update)
  saveRole() {
    if (this.roleForm.valid) {
      const formValue = this.roleForm.value;
      
      if (this.modalMode === 'create') {
        const roleDto: RoleDto = {
          displayName: formValue.displayName.trim()
        };
        this.store.dispatch(RoleActions.createRole({ roleDto }));
      } else if (this.selectedRole) {
        const roleDto: RoleDto = {
          uuid: this.selectedRole.uuid,
          displayName: formValue.displayName.trim()
        };
        this.store.dispatch(RoleActions.updateRole({ roleDto }));
      }
      
      this.closeModal();
    }
  }

  // Track by function for ngFor
  trackByRoleId(_index: number, role: Role): string {
    return role.uuid;
  }
}
