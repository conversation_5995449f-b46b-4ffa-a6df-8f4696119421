import {createAction, props} from '@ngrx/store';
import {User, UserFilterDto, UserCreateRequestDto, AssignRolesDto, UserListResponseDto} from '../../models/user.model';

// Load Users with Filters
export const loadUsers = createAction(
  '[User] Load Users',
  props<{ filters?: UserFilterDto }>()
);

export const loadUsersSuccess = createAction(
  '[User] Load Users Success',
  props<{ response: UserListResponseDto }>()
);

export const loadUsersFailure = createAction(
  '[User] Load Users Failure',
  props<{ error: any }>()
);

// Load User by UUID
export const loadUserByUuid = createAction(
  '[User] Load User By UUID',
  props<{ uuid: string }>()
);

export const loadUserByUuidSuccess = createAction(
  '[User] Load User By UUID Success',
  props<{ user: User }>()
);

export const loadUserByUuidFailure = createAction(
  '[User] Load User By UUID Failure',
  props<{ error: any }>()
);

// Save User (Create or Update)
export const saveUser = createAction(
  '[User] Save User',
  props<{ userDto: UserCreateRequestDto }>()
);

export const saveUserSuccess = createAction(
  '[User] Save User Success',
  props<{ user: User }>()
);

export const saveUserFailure = createAction(
  '[User] Save User Failure',
  props<{ error: any }>()
);

// Delete User
export const deleteUser = createAction(
  '[User] Delete User',
  props<{ uuid: string }>()
);

export const deleteUserSuccess = createAction(
  '[User] Delete User Success',
  props<{ uuid: string }>()
);

export const deleteUserFailure = createAction(
  '[User] Delete User Failure',
  props<{ error: any }>()
);

// Deactivate User
export const deactivateUser = createAction(
  '[User] Deactivate User',
  props<{ uuid: string }>()
);

export const deactivateUserSuccess = createAction(
  '[User] Deactivate User Success',
  props<{ user: User }>()
);

export const deactivateUserFailure = createAction(
  '[User] Deactivate User Failure',
  props<{ error: any }>()
);

// Activate User
export const activateUser = createAction(
  '[User] Activate User',
  props<{ uuid: string }>()
);

export const activateUserSuccess = createAction(
  '[User] Activate User Success',
  props<{ user: User }>()
);

export const activateUserFailure = createAction(
  '[User] Activate User Failure',
  props<{ error: any }>()
);

// Assign Roles to User
export const assignRolesToUser = createAction(
  '[User] Assign Roles To User',
  props<{ assignRolesDto: AssignRolesDto }>()
);

export const assignRolesToUserSuccess = createAction(
  '[User] Assign Roles To User Success',
  props<{ user: User }>()
);

export const assignRolesToUserFailure = createAction(
  '[User] Assign Roles To User Failure',
  props<{ error: any }>()
);

// Clear User Error
export const clearUserError = createAction(
  '[User] Clear Error'
);

// Set Selected User
export const setSelectedUser = createAction(
  '[User] Set Selected User',
  props<{ uuid: string | null }>()
);

// Set User Filters
export const setUserFilters = createAction(
  '[User] Set User Filters',
  props<{ filters: UserFilterDto }>()
);
