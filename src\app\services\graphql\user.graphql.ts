import { gql } from 'apollo-angular';

// Fragment for user fields to reuse across queries
const USER_FIELDS = gql`
  fragment UserFields on UserResponseDto {
    uuid
    username
    fullName
    email
    phoneCode
    phone
    status
    roles {
      uuid
      name
      displayName
    }
    lastLogin
    createdAt
  }
`;

// Simple test query without filters
export const LIST_USERS_SIMPLE = gql`
  query {
    listUsers {
      status
      code
      dataList {
        uuid
        username
        fullName
        email
        phoneCode
        phone
        status
        roles {
          uuid
          name
          displayName
        }
        lastLogin
        createdAt
      }
      extras
      errorDescription
    }
  }
`;

// Queries
export const LIST_USERS = gql`
  query ListUsers($filter: UserFilterInput) {
    listUsers(filter: $filter) {
      status
      code
      dataList {
        ...UserFields
      }
      extras
      errorDescription
    }
  }
  ${USER_FIELDS}
`;

export const GET_USER_BY_UUID = gql`
  query GetUserByUuid($uuid: String!) {
    getUserByUuid(uuid: $uuid) {
      status
      code
      data {
        ...UserFields
      }
      errorDescription
    }
  }
  ${USER_FIELDS}
`;

// Mutations
export const SAVE_USER = gql`
  mutation SaveUser($userDto: UserCreateRequestDtoInput!) {
    saveUser(userDto: $userDto) {
      status
      code
      data {
        ...UserFields
      }
      errorDescription
    }
  }
  ${USER_FIELDS}
`;

export const DEACTIVATE_USER = gql`
  mutation DeactivateUser($uuid: String!) {
    deactivateUser(uuid: $uuid) {
      status
      code
      data {
        ...UserFields
      }
      errorDescription
    }
  }
  ${USER_FIELDS}
`;

export const ACTIVATE_USER = gql`
  mutation ActivateUser($uuid: String!) {
    activateUser(uuid: $uuid) {
      status
      code
      data {
        ...UserFields
      }
      errorDescription
    }
  }
  ${USER_FIELDS}
`;

export const ASSIGN_ROLES_TO_USER = gql`
  mutation AssignRolesToUser($assignRolesDto: AssignRolesDto!) {
    assignRolesToUser(assignRolesDto: $assignRolesDto) {
      status
      code
      data {
        ...UserFields
      }
      errorDescription
    }
  }
  ${USER_FIELDS}
`;

export const DELETE_USER = gql`
  mutation DeleteUser($uuid: String!) {
    deleteUser(uuid: $uuid) {
      status
      code
      errorDescription
    }
  }
`;
