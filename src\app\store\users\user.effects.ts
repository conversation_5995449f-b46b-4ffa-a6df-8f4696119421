import {Injectable} from '@angular/core';
import {Actions, createEffect, ofType} from '@ngrx/effects';
import {catchError, map, mergeMap, tap} from 'rxjs/operators';
import {of} from 'rxjs';
import * as UserActions from './user.actions';
import {Apollo} from 'apollo-angular';
import {NotificationService} from '../../services/notification.service';
import {
  LIST_USERS,
  LIST_USERS_SIMPLE,
  GET_USER_BY_UUID,
  SAVE_USER,
  DELETE_USER,
  DEACTIVATE_USER,
  ACTIVATE_USER,
  ASSIGN_ROLES_TO_USER
} from '../../services/graphql/user.graphql';
import {
  GqlUserResponse,
  UserListResponseDto,
  User,
  UserCreateRequestDto,
  AssignRolesDto
} from '../../models/user.model';

@Injectable()
export class UserEffects {

  loadUsers$ = createEffect(() => this.actions$.pipe(
    ofType(UserActions.loadUsers),
    mergeMap(({ filters }) => {
      console.log('Loading users with filters:', filters);

      // Check if user is authenticated
      const token = localStorage.getItem('currentClient');
      console.log('Token available:', !!token);
      console.log('Token value:', token ? token.substring(0, 20) + '...' : 'null');

      return this.apollo.query<{listUsers: GqlUserResponse}>({
        query: LIST_USERS_SIMPLE,
        fetchPolicy: 'network-only'
      }).pipe(
        tap(result => {
          console.log('GraphQL Response:', result);
        }),
        map(result => {
          console.log('Full GraphQL result:', result);

          if (!result.data) {
            console.error('No data in GraphQL result:', result);
            throw new Error('No data received from server');
          }

          if (!result.data.listUsers) {
            console.error('No listUsers in result.data:', result.data);
            throw new Error('listUsers query not found in response');
          }

          const response = result.data.listUsers;
          console.log('Parsed response:', response);

          if (response.code === 'SUCCESS' && response.dataList) {
            const userListResponse: UserListResponseDto = {
              users: response.dataList,
              totalCount: response.extras?.totalCount || 0,
              page: response.extras?.page || 0,
              size: response.extras?.size || 10,
              totalPages: response.extras?.totalPages || 0
            };
            console.log('User list response:', userListResponse);
            return UserActions.loadUsersSuccess({ response: userListResponse });
          } else {
            console.error('GraphQL response error:', response);
            throw new Error(response?.errorDescription || 'Failed to load users');
          }
        }),
        catchError(error => {
          console.error('Load Users Error:', error);
          this.notificationService.showError('Failed to load users: ' + (error.message || error));
          return of(UserActions.loadUsersFailure({ error }));
        })
      );
    })
  ));

  loadUserByUuid$ = createEffect(() => this.actions$.pipe(
    ofType(UserActions.loadUserByUuid),
    mergeMap(({ uuid }) => this.apollo.query<{getUserByUuid: GqlUserResponse}>({
      query: GET_USER_BY_UUID,
      variables: { uuid },
      fetchPolicy: 'network-only'
    }).pipe(
      map(result => {
        const response = result.data.getUserByUuid;
        if (response.status === 'SUCCESS' && response.data) {
          return UserActions.loadUserByUuidSuccess({ user: response.data });
        } else {
          throw new Error(response.errorDescription || 'Failed to load user');
        }
      }),
      catchError(error => {
        console.error('Load User By UUID Error:', error);
        this.notificationService.showError('Failed to load user');
        return of(UserActions.loadUserByUuidFailure({ error }));
      })
    ))
  ));

  saveUser$ = createEffect(() => this.actions$.pipe(
    ofType(UserActions.saveUser),
    mergeMap(({ userDto }) => this.apollo.mutate<{saveUser: GqlUserResponse}>({
      mutation: SAVE_USER,
      variables: { userDto }
    }).pipe(
      map(result => {
        const response = result.data?.saveUser;
        console.log("Save user response: " + response)
        if (response?.code == 'SUCCESS' && response.data) {
          const isUpdate = 'uuid' in userDto;
          this.notificationService.showSuccess(
            isUpdate ? 'User updated successfully' : 'User created successfully'
          );
          return UserActions.saveUserSuccess({ user: response.data });
        } else {
          throw new Error(response?.errorDescription || 'Failed to save user');
        }
      }),
      catchError(error => {
        console.error('Save User Error:', error);
        this.notificationService.showError('Failed to save user');
        return of(UserActions.saveUserFailure({ error }));
      })
    ))
  ));

  deleteUser$ = createEffect(() => this.actions$.pipe(
    ofType(UserActions.deleteUser),
    mergeMap(({ uuid }) => this.apollo.mutate<{deleteUser: GqlUserResponse}>({
      mutation: DELETE_USER,
      variables: { uuid }
    }).pipe(
      map(result => {
        const response = result.data?.deleteUser;
        if (response?.status === 'SUCCESS') {
          this.notificationService.showSuccess('User deleted successfully');
          return UserActions.deleteUserSuccess({ uuid });
        } else {
          throw new Error(response?.errorDescription || 'Failed to delete user');
        }
      }),
      catchError(error => {
        console.error('Delete User Error:', error);
        this.notificationService.showError('Failed to delete user');
        return of(UserActions.deleteUserFailure({ error }));
      })
    ))
  ));

  deactivateUser$ = createEffect(() => this.actions$.pipe(
    ofType(UserActions.deactivateUser),
    mergeMap(({ uuid }) => this.apollo.mutate<{deactivateUser: GqlUserResponse}>({
      mutation: DEACTIVATE_USER,
      variables: { uuid }
    }).pipe(
      map(result => {
        const response = result.data?.deactivateUser;
        if (response?.status === 'SUCCESS' && response.data) {
          this.notificationService.showSuccess('User deactivated successfully');
          return UserActions.deactivateUserSuccess({ user: response.data });
        } else {
          throw new Error(response?.errorDescription || 'Failed to deactivate user');
        }
      }),
      catchError(error => {
        console.error('Deactivate User Error:', error);
        this.notificationService.showError('Failed to deactivate user');
        return of(UserActions.deactivateUserFailure({ error }));
      })
    ))
  ));

  activateUser$ = createEffect(() => this.actions$.pipe(
    ofType(UserActions.activateUser),
    mergeMap(({ uuid }) => this.apollo.mutate<{activateUser: GqlUserResponse}>({
      mutation: ACTIVATE_USER,
      variables: { uuid }
    }).pipe(
      map(result => {
        const response = result.data?.activateUser;
        if (response?.status === 'SUCCESS' && response.data) {
          this.notificationService.showSuccess('User activated successfully');
          return UserActions.activateUserSuccess({ user: response.data });
        } else {
          throw new Error(response?.errorDescription || 'Failed to activate user');
        }
      }),
      catchError(error => {
        console.error('Activate User Error:', error);
        this.notificationService.showError('Failed to activate user');
        return of(UserActions.activateUserFailure({ error }));
      })
    ))
  ));

  assignRolesToUser$ = createEffect(() => this.actions$.pipe(
    ofType(UserActions.assignRolesToUser),
    mergeMap(({ assignRolesDto }) => this.apollo.mutate<{assignRolesToUser: GqlUserResponse}>({
      mutation: ASSIGN_ROLES_TO_USER,
      variables: { assignRolesDto }
    }).pipe(
      map(result => {
        const response = result.data?.assignRolesToUser;
        if (response?.status === 'SUCCESS' && response.data) {
          this.notificationService.showSuccess('Roles assigned successfully');
          return UserActions.assignRolesToUserSuccess({ user: response.data });
        } else {
          throw new Error(response?.errorDescription || 'Failed to assign roles');
        }
      }),
      catchError(error => {
        console.error('Assign Roles Error:', error);
        this.notificationService.showError('Failed to assign roles');
        return of(UserActions.assignRolesToUserFailure({ error }));
      })
    ))
  ));

  constructor(
    private actions$: Actions,
    private apollo: Apollo,
    private notificationService: NotificationService
  ) {}
}
